/**
 Colors:
 
  - Header: #333333
  - Subheader: #e8edf1
  - Subheader border: #a4acb9
  - Highlight buttons/text: #36a7c4
  - Text color: #1a1a1a
  - Filter color: #373e48
  - Dark highlight: #606b7d

 */
.container-fluid {
	max-width: 1400px;
	min-width: 1000px;
	margin: 0 auto;
}

.subtle-select .glyphicon {
    visibility: hidden;
    padding-left: 5px;
}

a.subtle-select:hover .glyphicon {
    visibility: visible;
}

.full {
	padding: 0 15px;
	width: 100%;
}

.inline {
    display: inline;
}

.greyish {
    color: #afafaf;
}

.roweditor-canvas {
    margin-top: 50px;
}

.no-pad {
	margin: 0;
	max-width: 1300px;
    min-width: 1100px;
}

.content.no-pad {
    max-width: 100%;
    min-width: 100%;
}

.inset .col-sm-3 {
    margin-left: -15px;
}
.no-pad > div{
	padding: 0;
}


.dropdown-toggle .icon-caret-down {
	padding-left: 10px;
	font-size: 85%;
}

h1 {
	margin: 0 0 0 15px;
	padding: 0;
	font-size: 22px;
	line-height: 40px;
	border: none;
	color: #ffffff;
	font-family: 'Lato', sans-serif; 
}

.truncate, .truncate > span {
  white-space: nowrap;
  width: 100%;                   
  overflow: hidden; 
  text-overflow: ellipsis;
}

.subheader .details .counter {
	top: -1px;
	line-height: 1;
	display: inline-block;
	padding: 2px 6px;
	min-width: 20px;
	background-color: #e8edf1;
	color: #333333;
}

.subheader .subtle-select {
	margin: -6px 0 0 -8px;
}

.btn .icon-and-label {
	padding-right: 5px;
} 

.dropdown-menu .title {
	margin: 5px 10px 0px 10px;
	font-size: 17px;
	min-width: 250px;
}

.dropdown-menu ul {
	list-style: none;
	list-style-position: inside;
	padding: 5px 10px;
}

.input-group-addon {
	background-color: transparent;
}

/* List Filter */
.filter-wrapper {
	min-height: 400px;
	margin-top: 10px;
    margin-left: -15px;
}

ul.filter-list {
	list-style: none;
	list-style-position: inside;
	padding-left: 0px;
	padding-top: 10px;
}

ul.filter-list li a {
	display: block;
	color: #373e48;
	font-size: 17px;
	margin: 10px 5px 10px 0px;
	padding-left: 10px;
}

ul.filter-list li.current a {
	color: #36a7c4;
	padding-left: 5px;
	border-left: 4px solid #36a7c4;
}

ul.filter-list li a:hover, ul.filter-list li a:focus {
	text-decoration: none;
	background-color: #e8edf1;
}

ul.filter-list li.current a:hover, ul.filter-list li.current a:focus {
	background-color: transparent;
	color: #36a7c4;
	cursor: default;
	text-decoration: none;
}


/* Result items */


.item-wrapper {
	padding-left: 0;
	margin-top: 5px;
}

.item-wrapper .message {
	text-align: left;
	margin-left: 5px;
	line-height: 40px;
	color: #606b7d;
}
.item-wrapper .message span {
	font-size: 14px;	
}

.item-wrapper .item {
	width: 25%;
	padding: 0;
	margin: 0;
	float: left;
}

.item-wrapper .item .btn-default.disabled, 
.item-wrapper .item .btn-default[disabled],
.item-wrapper .item .btn-default[disabled]:active,
.item-wrapper .item .btn-default[disabled]:hover {
	border-color: #ffffff;
	cursor: default;	
}

.item-wrapper .item .item-box {
	margin: 5px;
	border: 1px solid #e8edf1;
	height: 250px;
	overflow: hidden;
	cursor: pointer;
	background-repeat: no-repeat;
	background-position: center 20px;
	background-size: auto;
    position: relative;
}

.item-box .details {
    position: relative;
	background-color: #e8edf1;
	height: 160px;
	margin-top: 120px;
	padding: 5px;
	color: #373e48;
	font-size: 13px;
	
	transition: margin-top .5s ease;
	-moz-transition: margin-top .5s ease;
	-webkit-transition: margin-top .5s ease;
	-o-transition: margin-top .5s ease;
}

.item-box:hover .details, .item-box.active .details {
	margin-top: 50px;
}

.item-box .actions {
	padding: 5px;
	height: 45px;
}

.item-box .actions .btn-group {
	visibility: hidden;
}

.item-box:hover .actions .btn-group, .item-box.active .actions .btn-group {
	visibility: inherit;
}


.item-box .details h3 {
	font-size: 14px;
	margin: 0;
	padding: 2px;
	color: #373e48;
}

.item-box .details span {
	display: block;
	margin-top: 5px;
}

.item-box .details span i {
	padding-right: 10px;
	padding-left: 5px;
}

.item-box .details .basic-details {
	min-height: 60px;
}

.item-box .details p {
	width: 100%;
	height: 70px;
	font-size: 12px;
	overflow: hidden;
}

.create-inline {
	padding: 100px 20px 80px 20px;
	border: 1px solid #e8edf1;
}
.create-inline span {
	display: block;
	font-size: 18px;
	color: #1a1a1a;
	text-align: center;
	margin-bottom: 20px;
} 

.create-inline .glyphicon {
	margin-right: 10px;
}

.show-more {
	clear: both;
	height: 50px;
	text-align: center;
	padding-top: 5px;
	margin: 5px;
}

.show-more a {
	display: block;
	padding: 5px;
	font-size: 15px;
	text-decoration: none;
	cursor: pointer;
	color: #666666;
}

.show-more a:hover {
	color: #1a1a1a;
	background: #e8edf1;
}

.content-canvas-wrapper {
    -moz-box-shadow:    inset  0  3px 3px -4px #ababab;
    -webkit-box-shadow: inset  0  3px 3px -4px #ababab;
    box-shadow:        inset  0  3px 3px -4px #ababab;
    margin: 15px 7px 0 7px;
    z-index: 0;
}
.content-canvas {
    background-color: #f9f9f9;
    margin: 0 3px 0 3px;

    -moz-box-shadow:    inset  0  3px 3px -4px #ababab;
    -webkit-box-shadow: inset  0  3px 3px -4px #ababab;
    box-shadow:        inset  0  3px 3px -4px #ababab;

    padding: 20px;
}


.content-canvas h3 {
    margin-bottom: 5px;
}

.content-canvas .no-results{
    color: #999999;
    font-size: 16px;
    margin: 10px 0px;
}

.content-canvas .item-wrapper {
    margin: 5px 10px;
}

/* History */
table.history {
	margin: 0;
	padding: 0;
}

.subheader table.history {
	min-width: 250px;	
}

table.history td {
	vertical-align: middle;	
}

table.history tr td:last-child {
	width: 90%;
}

table.history tr:hover {
	background-color: #f3f6f8;
	cursor: pointer;
}
table.history tr.current:hover {
	background-color: #e8edf1;
	cursor: inherit;
}


table.history a:hover {
	text-decoration: none;
	
}

table.history .version {
	font-size: 30px;
	display: inline-block;
	color: #36a7c4;
	padding: 5px 10px;
	vertical-align:middle;
	color: #36a7c4;
}

table.history .detail {
	padding: 5px 5px;
	font-size: 15px;
	color: #1a1a1a;
	display: inline-block;
}

table.history tr.current {
	font-weight: bold;
	background-color: #e8edf1;
}
table.history tr.current td {
	background-color: #e8edf1;
}

.comments {
	clear: both;
	width: 350px;
	border-top: 1px solid #eeeeee;
	margin-top: 5px;
	max-height: 350px;
	overflow: auto;	
}

.comment {
	margin: 10px 0px 20px 0px;	
	font-size: 12px;
}

.comment .date {
	color: #999999;
	font-size: 12px;
}

.comment .author {
	color: #36a7c4;
	font-size: 18px;
}

.comment p {
	 word-wrap: break-word;
}

.modal.modal-wide .modal-dialog {
	width: 1000px;	
}

.modal-dialog.modal-wide {
    width: 1000px;  
}

.modal-body p {
	font-size: 15px;
}

.modal-body p.danger {
	color: #d35f5f;
	margin-top: 10px;
}

.form-group .inline-help {
    font-size: 11px;
    color: #666666;
    margin-top: 5px;
}

.form-group .message {
    color: #1a1a1a;
    font-size: 14px;
}

.people-select > .selection {
    width: 100%;
    text-align: left;
}
.popup-wrapper .people-select {
    max-height: 160px;
}

.people-select .nothing-to-see {
    padding: 5px 0;

    color: #999999;
}

.inline-people-select {
    max-height: 120px;
    overflow: auto;
}

/** Center tabbed pane */
.center-pane {
    overflow: auto;
    padding-bottom: 20px;
}

.center-pane .content {
    overflow: hidden;
}
.center-pane .tab-actions {
    padding: 8px;
}

.center-pane .tabs-wrapper > .pull-right {
    margin-right: 5px;
}
.center-pane .content {
    padding: 10px;
}

.center-pane.content {
    padding: 0;
}

.center-pane .content .tabs, .center-pane.content .tabs {
    padding-left: 15px;
}

.center-pane .content .tabs > li a, .center-pane.content .tabs > li a {
    padding: 8px 30px;
}

.center-pane .header h1 {
    font-size: 30px;
    margin: 0;
    padding:0;
}

.center-pane .header h2 {
    font-size: 24px;
    margin: 0 0 5px 0;
    padding:0;
}

.center-pane .header {
    padding: 5px 10px 25px 10px;
}


.center-pane .header.compact {
    padding-bottom: 5px;
}

.center-pane .well {
    -moz-border-radius: 0px;
    -webkit-border-radius: px;
    border-radius: 0px;
    background-color: #f9f9f9;
    padding: 12px 10px;
    margin: 15px 0 0 0;
    border: 1px solid #eeeeee;
}

/** General button styling */
.btn.btn-clean {
    border: none;
    background-color: transparent;
    font-size: 24px;
    padding: 2px 6px;
    color: #444444;

    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.btn-clean:hover .icon-remove {
    color: #a02828;
}

.btn-clean:focus, .btn-clean:hover {
    color: #5f8dd3;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}


.btn-clean:active {
    color: #2c5aa0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

/* Show list in popup */

ul.list {
    list-style: none inside;
    padding: 0px;
    margin-bottom: 3px;
}

ul.list>li {
    line-height: 30px;
    margin: 0;
    padding: 4px;
    cursor: pointer;
}

.popup-wrapper ul.list>li {
    border-top: 1px solid #eeeeee;
}

.popup-wrapper ul.list>li:last-child {
    border-bottom: 1px solid #eeeeee;
}

ul.list>li:hover, ul.list>li.active {
    background-color: #f2f2f2;
}

ul.list >li .actions {
    float:right;
    margin: 0px 0px 0px 5px;
    visibility: hidden;
}

ul.list>li:hover .actions {
    visibility: inherit;
}

/** Animations **/
.fadein.ng-enter, 
.fadein.ng-move {
  -webkit-transition: 0.5s linear opacity;
  transition: 0.5s linear all;
}

.fadein.ng-enter {
  opacity:0;
}
.fadein.ng-enter.ng-enter-active {
  opacity:1;
}

.fadein.ng-move {
  opacity:0.5;
}
.fadein.ng-move.ng-move-active {
  opacity:1;
}

.popup-error {
    color: red;
    padding: 0 5px 8px 0;
}

/** Passwords */

.password-field {
    width: 320px;
}

/** LOADING */

.message .loading {
	line-height: 40px;
	margin-left: 0px;	
}