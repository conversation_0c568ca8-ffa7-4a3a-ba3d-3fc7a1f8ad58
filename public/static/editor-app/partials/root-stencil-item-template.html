<span class="stencil-item root-stencil-item"
      id="{{group.id}}"
      title="{{group.description}}"
      ng-model="draggedElement"
      data-drag="true"
      jqyoui-draggable="{onStart:'startDragCallback', onDrag:'dragCallback'}"
      data-jqyoui-options="{revert: 'invalid', helper: 'clone', opacity : 0.5}">
        
        <img ng-src="editor-app/stencilsets/bpmn2.0/icons/{{group.icon}}" width="16px;" height="16px;"/>
        {{group.name}}
</span>