
<div class="modal" ng-controller="KisBpmSequenceFlowOrderPopupCtrl">
    <div class="modal-dialog">
        <div class="modal-content">
			<div class="modal-header">
			    <button type="button" class="close" data-dismiss="modal" aria-hidden="true" ng-click="close()">&times;</button>
			    <h3>{{'PROPERTY.PROPERTY.EDIT.TITLE' | translate:property}}</h3>
			</div>
			
			<div class="modal-body">
			
			    <div translate>PROPERTY.SEQUENCEFLOW.ORDER.DESCRIPTION</div>
			    <br/>
			    <ol>
			        <li class="sequence-flow-order-element" ng-repeat="sequenceFlow in outgoingSequenceFlow">
			            {{'PROPERTY.SEQUENCEFLOW.ORDER.SEQUENCEFLOW.VALUE' | translate:sequenceFlow}}
			            <a class="btn btn-icon btn-sm"
			               rel="tooltip"
			               data-title="{{'ACTION.MOVE.UP' | translate}}"
			               data-placement="bottom"
			               data-original-title="" title=""
			               ng-click="moveUp($index)"
			               ng-if="$index > 0">
			                 <i class="glyphicon glyphicon-arrow-up"></i>
			            </a>
			            <a class="btn btn-icon btn-sm"
			               rel="tooltip"
			               data-title="{{'ACTION.MOVE.DOWN' | translate}}"
			               data-placement="bottom"
			               data-original-title=""
			               title=""
			               ng-click="moveDown($index)"
			               ng-if="$index < outgoingSequenceFlow.length - 1">
			                 <i class="glyphicon glyphicon-arrow-down"></i>
			            </a>
			        </li>
			    </ol>
			
			
			</div>
			<div class="modal-footer">
			    <button ng-click="cancel()" class="btn btn-primary" translate>ACTION.CANCEL</button>
			    <button ng-click="save()" class="btn btn-primary" translate>ACTION.SAVE</button>
			</div>
		</div>
	</div>
</div>
