#user  nobody;
worker_processes  1;
events {
    worker_connections  1024;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;
    #gzip  on;

   server {
        listen       80;
        server_name  localhost;

        location / {
                   root   /usr/share/nginx/html/dist;
                               try_files $uri $uri/ /index.html;
                   index  index.html index.htm;
               }
    }

}
