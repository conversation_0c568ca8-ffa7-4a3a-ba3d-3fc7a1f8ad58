import { getInfo as fetchUserInfo, logout } from '@/api/loginApi'
import { getAllI18n } from '@/api/system/international/International'
import { LanguageEnum } from '@/enums/appEnum'
import i18n from '@/locales'
import { router } from '@/router'
import usePermissionStore from '@/store/modules/permission'
import { removeToken } from '@/utils/auth'
import avatar from '@imgs/user/avatar.webp'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useSettingStore } from './setting'
import { useWorktabStore } from './worktab'
import { AppRouteRecord } from '@/types/router'
import { setPageTitle } from '@/router/utils/utils'
import { resetRouterState } from '@/router/guards/beforeEach'
import { RoutesAlias } from '@/router/routesAlias'
import { useMenuStore } from './menu'

/**
 * 用户状态管理
 * 管理用户登录状态、个人信息、语言设置、搜索历史、锁屏状态等
 */
export const useUserStore = defineStore(
  'userStore',
  () => {
    // 语言设置
    // 状态定义为 ref
    const id = ref('')
    const name = ref('')
    const language = ref(LanguageEnum.ZH)
    // 登录状态
    const isLogin = ref(false)
    // 锁屏状态
    const token = ref('')
    const permissions = ref<string>([])
    const roles = ref([] as string[])
    const i18nData = ref({})
    const isLock = ref(false)
    // 锁屏密码
    const lockPassword = ref('')
    // 用户信息
    const info = ref<Partial<Api.User.UserInfo>>({})
    // 搜索历史记录
    const searchHistory = ref<AppRouteRecord[]>([])
    // 访问令牌
    const accessToken = ref('')
    // 刷新令牌
    const refreshToken = ref('')

    // 计算属性：获取用户信息
    const getUserInfo = computed(() => info.value)
    // 计算属性：获取设置状态
    const getSettingState = computed(() => useSettingStore().$state)
    // 计算属性：获取工作台状态
    const getWorktabState = computed(() => useWorktabStore().$state)

    /**
     * 设置权限
     * @param newPermissions 新的权限列表
     */
    const setPermissions = (newPermissions: string[]) => {
      permissions.value = newPermissions
    }

    /**
     * 设置角色
     * @param newRoles 新的角色列表
     */
    const setRoles = (newRoles: string[]) => {
      roles.value = newRoles
    }

    /**
     * 设置登录状态
     * @param status 登录状态
     */
    const setLoginStatus = (status: boolean) => {
      isLogin.value = status
    }

    // 获取用户信息
    const getUserInformation = (forceReloadI18n = false) => {
      return new Promise((resolve, reject) => {
        // 使用 Promise.all 并发执行 getInfo 和 initI18nMessages
        Promise.all([
          fetchUserInfo(), // 使用重命名的 API 函数
          initI18nMessages(forceReloadI18n) // 根据参数决定是否强制重新加载
        ])
          .then(([res]) => {
            const user = res.user
            if (res.roles && res.roles.length > 0) {
              // 验证返回的 roles 是否是一个非空数组
              setRoles(res.roles)
              setPermissions(res.permissions)
            } else {
              roles.value = ['ROLE_DEFAULT' as string]
            }
            setUserInfo(user)
            // 设置用户信息
            id.value = user.userId
            name.value = user.userName
            localStorage.setItem('username', name.value)
            resolve(res) // 返回用户信息
          })
          .catch((error) => {
            reject(error) // 捕获任何一个请求的错误
          })
      })
    }

    /**
     * 设置语言
     * @param lang 语言枚举值
     */
    const setLanguage = (lang: LanguageEnum) => {
      setPageTitle(router.currentRoute.value)
      language.value = lang
    }
    // 异步加载语言数据
    const initI18nMessages = async (forceReload = false) => {
      try {
        // 检查是否已有缓存数据且不强制重新加载
        if (i18nData.value && Object.keys(i18nData.value).length > 0 && !forceReload) {
          console.debug('[i18n] 使用缓存的国际化数据')
          // 将缓存的语言数据注入 i18n 实例
          Object.keys(i18nData.value).forEach((locale) => {
            i18n.global.setLocaleMessage(locale, i18nData.value[locale])
          })
          // 设置初始语言
          i18n.global.locale = language.value
          return
        }

        // 只有在没有缓存数据或强制重新加载时才请求接口
        console.debug('[i18n] 从服务器获取国际化数据')
        const { data } = await getAllI18n()
        i18nData.value = data

        // 将获取的语言数据注入 i18n 实例
        Object.keys(i18nData.value).forEach((locale) => {
          i18n.global.setLocaleMessage(locale, i18nData.value[locale])
        })
        // 设置初始语言
        i18n.global.locale = language.value
      } catch (error) {
        console.error('加载语言包失败', error)
        // 如果请求失败但有缓存数据，使用缓存数据
        if (i18nData.value && Object.keys(i18nData.value).length > 0) {
          console.warn('[i18n] 请求失败，使用缓存数据')
          Object.keys(i18nData.value).forEach((locale) => {
            i18n.global.setLocaleMessage(locale, i18nData.value[locale])
          })
          i18n.global.locale = language.value
        }
      }
    }
    /**
     * 设置用户信息
     * @param userInfo 用户信息
     */
    const setUserInfo = (userInfo: Api.User.UserInfo) => {
      const avatar1 = userInfo.avatar
      if (!avatar1) {
        userInfo.avatar = avatar
      } else {
        if (!avatar1.startsWith('http')) {
          userInfo.avatar =
            avatar1 == '' || avatar1 == null
              ? avatar
              : import.meta.env.VITE_BASE_URL + userInfo.avatar
        }
      }
      info.value = userInfo
    }
    /**
     * 设置搜索历史
     * @param list 搜索历史列表
     */
    const setSearchHistory = (list: AppRouteRecord[]) => {
      searchHistory.value = list
    }

    /**
     * 设置锁屏状态
     * @param status 锁屏状态
     */
    const setLockStatus = (status: boolean) => {
      isLock.value = status
    }

    /**
     * 设置锁屏密码
     * @param password 锁屏密码
     */
    const setLockPassword = (password: string) => {
      lockPassword.value = password
    }

    /**
     * 设置令牌
     * @param newAccessToken 访问令牌
     * @param newRefreshToken 刷新令牌（可选）
     */
    const setToken = (newAccessToken: string, newRefreshToken?: string) => {
      accessToken.value = newAccessToken
      if (newRefreshToken) {
        refreshToken.value = newRefreshToken
      }
    }

    /**
     * 退出登录
     * 清空所有用户相关状态并跳转到登录页
     */
    const logOut = () => {
      // 清空用户信息
      info.value = {}
      // 重置登录状态
      isLogin.value = false
      // 重置锁屏状态
      isLock.value = false
      // 清空锁屏密码
      lockPassword.value = ''
      // 清空访问令牌
      accessToken.value = ''
      // 清空刷新令牌
      refreshToken.value = ''
      // 清空工作台已打开页面
      useWorktabStore().opened = []
      // 移除iframe路由缓存
      sessionStorage.removeItem('iframeRoutes')
      // 清空主页路径
      useMenuStore().setHomePath('')
      // 重置路由状态
      resetRouterState()
      // 跳转到登录页
      router.push(RoutesAlias.Login)
      logout()
        .then(() => {
          info.value = {}
          token.value = ''
          permissions.value = []
          roles.value = []
          isLogin.value = false
          isLock.value = false
          lockPassword.value = ''
          searchHistory.value = []
          useWorktabStore().opened = []
          usePermissionStore().menuList = []
          sessionStorage.removeItem('iframeRoutes')
          document.getElementsByTagName('html')[0].removeAttribute('class') // 移除暗黑主题
          router.push('/login')
        })
        .finally(() => {
          removeToken()
        })
    }

    return {
      id,
      name,
      language,
      isLogin,
      isLock,
      lockPassword,
      info,
      token,
      permissions,
      roles,
      i18nData,
      searchHistory,
      getUserInfo,
      getSettingState,
      getWorktabState,
      setUserInfo,
      setLoginStatus,
      setLanguage,
      setSearchHistory,
      setLockStatus,
      setLockPassword,
      // Actions
      // initState,
      // saveUserData,
      setPermissions,
      setRoles,
      setToken,
      getInfo: getUserInformation, // 重命名为 getInfo 以匹配原 API
      initI18nMessages,
      logOut
    }
  },
  {
    persist: {
      key: 'user',
      storage: localStorage
    }
  }
)
