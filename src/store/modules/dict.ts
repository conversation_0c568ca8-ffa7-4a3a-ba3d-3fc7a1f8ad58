import { defineStore } from 'pinia'
import { ref } from 'vue'

interface DictItem {
  key: string // or number, depending on your _key type
  value: any // replace with the actual type of your values
}

// Make sure dict is properly typed

const useDictStore = defineStore('dict', () => {
  // 状态使用 ref 定义
  const dict = ref<DictItem[]>([])

  // 获取字典
  function getDict(_key) {
    if (_key == null && _key == '') {
      return null
    }
    try {
      for (let i = 0; i < dict.value.length; i++) {
        if (dict.value[i].key == _key) {
          return dict.value[i].value
        }
      }
    } catch (e) {
      // 处理异常
      console.error('Error getting dictionary item:', e)
      return null
    }
    return null // 添加默认返回，确保函数始终有返回值
  }

  // 设置字典
  function setDict(_key, value) {
    if (_key !== null && _key !== '') {
      dict.value.push({
        key: _key,
        value: value
      })
    }
  }

  // 删除字典
  function removeDict(_key) {
    let bln = false
    try {
      for (let i = 0; i < dict.value.length; i++) {
        if (dict.value[i].key == _key) {
          dict.value.splice(i, 1)
          return true
        }
      }
    } catch (e) {
      // 处理异常
      console.error('Error removing dictionary item:', e)
      bln = false
    }
    return bln
  }

  // 清空字典
  function cleanDict() {
    dict.value = []
  }

  // 初始字典
  function initDict() {
    // 保持原有的空实现
  }

  // 返回所有状态和方法
  return {
    dict,
    getDict,
    setDict,
    removeDict,
    cleanDict,
    initDict
  }
})

export default useDictStore
