import { getRouters } from '@/api/menu'

import type { Component } from 'vue'

interface RouteItem {
  name: string
  path: string
  hidden?: boolean
  redirect?: string
  component: string | Component | (() => Promise<any>)
  alwaysShow?: boolean
  meta: {
    title: string
    icon: string
    noCache: boolean
    link: string | null
    perms: string
    menuCode: string
  }
  children?: RouteItem[]
}

const usePermissionStore = defineStore('permission', {
  state: () => ({
    menuList: [] as RouteItem[]
  }),
  actions: {
    setMenuList(menuList: RouteItem[]) {
      this.menuList = menuList
    },
    getMenuList() {
      return this.menuList
    },

    generateRoutes() {
      return new Promise<RouteItem[]>((resolve) => {
        // 向后端请求路由数据
        getRouters().then((res) => {
          const rdata = res.data
          const rewriteRoutes = filterAsyncRouter(rdata)
          this.setMenuList(rewriteRoutes)
          resolve(rewriteRoutes)
        })
      })
    }
  }
})

// 处理异步路由
function filterAsyncRouter(routes: RouteItem[], parentPath = ''): RouteItem[] {
  return routes.map((route) => {
    const newRoute = { ...route }
    // 处理完整路径
    const fullPath = parentPath ? `${parentPath}/${newRoute.path}` : newRoute.path
    newRoute.path = newRoute.path.startsWith('/') ? newRoute.path : fullPath

    // 处理组件
    if (newRoute.component) {
      if (
        route.component === 'Layout' ||
        route.component === 'InnerLink' ||
        route.component === 'ParentView'
      ) {
        newRoute.component = loadView(newRoute.component as string)
      } else {
        const component = loadView(newRoute.component as string)
        if (component) {
          newRoute.component = component
        }
      }
    }
    // 递归处理子路由
    if (newRoute.children && newRoute.children.length > 0) {
      newRoute.children = filterAsyncRouter(newRoute.children, newRoute.path)
    }

    return newRoute
  })
}

// 匹配views里面所有的.vue文件
const modules = import.meta.glob('@views/**/*.vue')

// 加载组件
export const loadView = (view: string): (() => Promise<any>) | null => {
  for (const path in modules) {
    const dir = path.split('views/')[1].split('.vue')[0]
    if (dir === view) {
      return () => modules[path]() // 返回异步函数
    }
  }
  return null
}

export default usePermissionStore
