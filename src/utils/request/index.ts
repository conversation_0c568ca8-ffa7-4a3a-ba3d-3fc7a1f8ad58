import axios, { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { ElLoading, ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import EmojiText from '../ui/emojo'
import { message } from '@/utils/resetMessage'
import errorCode from '@/utils/errorCode'
import { getCookieName, getToken } from '@/utils/auth'
import { blobValidate, formatStr, tansParams } from '@/utils/ruoyi'
import { saveAs } from 'file-saver'
import cache from '@/plugins/cache'
import { getLang } from '@/locales'

export const isRelogin = { show: false }
let downloadLoadingInstance

const axiosInstance = axios.create({
  timeout: 15000, // 请求超时时间(毫秒)
  baseURL: import.meta.env.VITE_BASE_URL, // API地址
  withCredentials: true, // 异步请求携带cookie
  transformRequest: [
    (data, headers) => {
      // 如果是 FormData，直接返回，不进行任何处理
      if (data instanceof FormData) {
        return data
      }

      // 如果是对象并且请求头是 JSON 类型，转换为 JSON 字符串
      if (headers['Content-Type'] === 'application/json;charset=utf-8') {
        return JSON.stringify(data)
      }

      // 默认情况直接返回数据（例如 URLSearchParams 等情况）
      return data
    }
  ], // 请求数据转换为 JSON 字符串
  validateStatus: (status) => status >= 200 && status < 300, // 只接受 2xx 的状态码
  headers: {
    get: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' },
    post: { 'Content-Type': 'application/json;charset=utf-8' },
    put: { 'Content-Type': 'application/json;charset=utf-8' }
  },
  transformResponse: [
    (data) => {
      // 响应数据转换
      try {
        return typeof data === 'string' && data.startsWith('{') ? JSON.parse(data) : data
      } catch {
        return data // 解析失败时返回原数据
      }
    }
  ]
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
    if (getToken() && !isToken) {
      config.headers[getCookieName()] = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    config.headers['Accept-Language'] = useUserStore().language

    if (config.method === 'get' && config.params) {
      let url = config.url + '?' + tansParams(config.params)
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    if (config.url.startsWith('/xxl')) {
      config.headers['username'] = localStorage.getItem('username')
      config.headers['ruoyi'] = 'ruoyi'
    }
    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      const requestObj = {
        url: config.url,
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime()
      }
      const requestSize = Object.keys(JSON.stringify(requestObj)).length // 请求数据大小
      const limitSize = 5 * 1024 * 1024 // 限制存放数据5M
      if (requestSize >= limitSize) {
        console.warn(`[${config.url}]: ` + '请求数据大小超出允许的5M限制，无法进行防重复提交验证。')
        return config
      }
      const sessionObj = cache.session.getJSON('sessionObj')
      //xxl-job请求需要在请求头添加用户名字

      if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
        cache.session.setJSON('sessionObj', requestObj)
      } else {
        const s_url = sessionObj.url // 请求地址
        const s_data = sessionObj.data // 请求数据
        const s_time = sessionObj.time // 请求时间
        const interval = 1000 // 间隔时间(ms)，小于此时间视为重复提交
        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          const message = getLang('request.processing')
          console.warn(`[${s_url}]: ` + message)
          return Promise.reject(new Error(message))
        } else {
          cache.session.setJSON('sessionObj', requestObj)
        }
      }
    }

    return config // 返回修改后的配置
  },
  (error) => {
    ElMessage.error(formatStr(getLang('service.error'), ` ${EmojiText[500]}`)) // 显示错误消息
    return Promise.reject(error) // 返回拒绝的 Promise
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (res: AxiosResponse) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default']

    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res.data
    }
    if (code === 401) {
      if (!isRelogin.show) {
        isRelogin.show = true
        ElMessageBox.confirm(getLang('session.expired'), getLang('system.message.prompt'), {
          confirmButtonText: getLang('reload.login'),
          cancelButtonText: getLang('common.cancel'),
          type: 'warning'
        })
          .then(() => {
            isRelogin.show = false
            useUserStore()
              .logOut()
              .then(() => {
                location.href = '/login'
              })
          })
          .catch(() => {
            isRelogin.show = false
          })
      }
      return Promise.reject(getLang('invalid.session'))
    } else if (code === 500) {
      // message.error(msg)
      message({ message: msg, type: 'error' })
      return Promise.reject(new Error(msg))
    } else if (code === 601) {
      message({ message: msg, type: 'warning' })
      return Promise.reject(new Error(msg))
    } else if (code !== 200) {
      ElNotification.error({ title: msg })
      return Promise.reject('error')
    } else {
      return Promise.resolve(res.data)
    }
  },
  (error) => {
    console.log('err' + error)
    let msg = error.message
    if (msg == 'Network Error') {
      msg = getLang('api.connection.error')
    } else if (msg.includes('timeout')) {
      msg = getLang('api.request.timeout')
    } else if (msg.includes('Request failed with status code')) {
      msg = formatStr(
        getLang('system.interface'),
        msg.substr(msg.length - 3),
        getLang('excption.tip')
      )
    }
    message({ message: msg, type: 'error', duration: 5 * 1000 })
    return Promise.reject(error)
  }
)

// 请求
async function request<T = any>(config: AxiosRequestConfig): Promise<T> {
  // 将 POST | PUT 请求的参数放入 data 中，并清空 params
  // if (config.method === 'POST' || config.method === 'PUT') {
  // config.data = config.params
  // config.params = {}
  // }
  try {
    return await axiosInstance.request<T>({ ...config })
  } catch (e) {
    if (axios.isAxiosError(e)) {
      // 可以在这里处理 Axios 错误
    }
    return Promise.reject(e)
  }
}

// API 方法集合
const api = {
  get<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'GET' }) // GET 请求
  },
  post<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'POST' }) // POST 请求
  },
  put<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'PUT' }) // PUT 请求
  },
  del<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'DELETE' }) // DELETE 请求
  }
}

// 通用下载方法
export function download(url, params, filename, config) {
  downloadLoadingInstance = ElLoading.service({
    text: getLang('data.download.inProgress'),
    background: 'rgba(0, 0, 0, 0.7)'
  })
  return api
    .post({
      url,
      data: params,
      headers: { 'Content-Type': 'application/json;charset=utf-8' },
      responseType: 'blob',
      ...config
    })
    .then(async (data) => {
      const isBlob = blobValidate(data)
      if (isBlob) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        const resText = await data.text()
        const rspObj = JSON.parse(resText)
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
        message.error(errMsg)
      }
      downloadLoadingInstance.close()
    })
    .catch((r) => {
      message.error(getLang('file.download.error'))
      downloadLoadingInstance.close()
    })
}

export function downloadQueryCase(url, data, filename, config) {
  downloadLoadingInstance = ElLoading.service({
    text: getLang('data.download.inProgress'),
    background: 'rgba(0, 0, 0, 0.7)'
  })
  return api
    .post({
      url,
      data,
      headers: { 'Content-Type': 'application/json;charset=utf-8' },
      responseType: 'blob',
      ...config
    })
    .then(async (data) => {
      const isBlob = blobValidate(data)
      if (isBlob) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        const resText = await data.text()
        const rspObj = JSON.parse(resText)
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
        message.error(errMsg)
      }
      downloadLoadingInstance.close()
    })
    .catch((r) => {
      message.error(getLang('file.download.error'))
      downloadLoadingInstance.close()
    })
}

export function uploadFile(fileArr: File[], categoryId: string, socketId: string, config) {
  downloadLoadingInstance = ElLoading.service({
    text: getLang('file.upload.inProgress'),
    background: 'rgba(0, 0, 0, 0.7)'
  })
  const formData = new FormData()
  for (let i = 0; i < fileArr.length; i++) {
    formData.append('files', fileArr[i]) // 'files' 是后端接收的字段名
  }

  let url = `/system/oss/qrCodeUploadImg`
  if (categoryId) {
    url += `?categoryId=${categoryId}`
  }
  if (socketId) {
    url += categoryId ? `&socketId=${socketId}` : `?socketId=${socketId}`
  }

  return api
    .post({
      url,
      data: formData,
      headers: { repeatSubmit: false },
      ...config
    })
    .then(async (data) => {
      downloadLoadingInstance.close()

      return data
    })
    .catch((r) => {
      message.error(getLang('upload.fail'))
      downloadLoadingInstance.close()
    })
}

export default request
