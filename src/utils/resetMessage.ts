import type { MessageHandler, MessageOptions } from 'element-plus'
import { ElMessage } from 'element-plus' //引入message弹出框

// 记录已经显示的消息，用Map存储，键为"内容+类型"，值为消息对象
const messageMap = new Map<string, MessageHandler>()

const resetMessage = (options: MessageOptions) => {
  // 获取当前消息内容和类型
  const currentMessage = typeof options === 'string' ? options : (options.message as string)
  const currentType = options.type || 'info'

  // 生成消息的唯一键
  const messageKey = `${currentType}:${currentMessage}`

  // 如果已经存在相同内容和类型的消息，直接返回对应的消息对象
  if (messageMap.has(messageKey)) {
    return messageMap.get(messageKey)!
  }

  // 不存在相同消息，显示新消息
  const messageDom = ElMessage(options)

  // 将新消息加入到Map中
  messageMap.set(messageKey, messageDom)

  // 当消息关闭时从Map中移除
  const originalClose = messageDom.close
  messageDom.close = () => {
    messageMap.delete(messageKey)
    originalClose()
  }

  return messageDom
}

type MessageType = 'success' | 'error' | 'warning' | 'info'
const typeArr: MessageType[] = ['success', 'error', 'warning', 'info']

typeArr.forEach((type) => {
  resetMessage[type] = (options: MessageOptions | string) => {
    if (typeof options === 'string') options = { message: options }
    options.type = type as any // 使用类型断言解决类型问题
    return resetMessage(options)
  }
})
export const message = resetMessage
