// 全局样式

@font-face {
  font-family: 'DMSans';
  font-style: normal;
  font-weight: 400;
  src: url(../fonts/DMSans.woff2) format('woff2');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: url(../fonts/Montserrat.woff2) format('woff2');
}

.btn-icon {
  font-size: 10px;
}

.el-btn-red {
  color: #fa6962 !important;

  &:hover {
    opacity: 0.9;
  }

  &:active {
    opacity: 0.7;
  }
}

// 顶部进度条颜色
#nprogress .bar {
  z-index: 2400;
  background-color: color-mix(in srgb, var(--main-color) 65%, white);
}

// 处理移动端组件兼容性
@media screen and (max-width: $device-phone) {
  * {
    cursor: default !important;
  }
}

// 背景滤镜
*,
::before,
::after {
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
}

// 色弱模式
.color-weak {
  filter: invert(80%);
  -webkit-filter: invert(80%);
}

#noop {
  display: none;
}

// 语言切换选中样式
.langDropDownStyle {
  // 选中项背景颜色
  .is-selected {
    background-color: rgba(var(--art-gray-200-rgb), 0.8) !important;
  }

  // 语言切换按钮菜单样式优化
  .lang-btn-item {
    .el-dropdown-menu__item {
      padding-left: 13px !important;
      padding-right: 6px !important;
      margin-bottom: 3px !important;
    }

    &:last-child {
      .el-dropdown-menu__item {
        margin-bottom: 0 !important;
      }
    }

    .menu-txt {
      min-width: 60px;
      display: block;
    }

    i {
      font-size: 10px;
      margin-left: 10px;
    }
  }
}

// 盒子默认边框
.page-content,
.art-custom-card {
  border: 1px solid var(--art-card-border) !important;
}

// 盒子边框
[data-box-mode='border-mode'] {
  .page-content,
  .art-custom-card,
  .art-table-card {
    border: 1px solid var(--art-card-border) !important;
  }

  .layout-sidebar {
    border-right: 1px solid var(--art-card-border) !important;
  }
}

// 盒子阴影
[data-box-mode='shadow-mode'] {
  .page-content,
  .art-custom-card,
  .art-table-card {
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04) !important;
    border: 1px solid rgba(var(--art-gray-300-rgb), 0.3) !important;
  }

  .layout-sidebar {
    border-right: 1px solid rgba(var(--art-gray-300-rgb), 0.4) !important;
  }
}

$base-bg-color: #f5f7fa;
$base-border-color: #eee;
$primary-color: var(--el-color-primary);

.bl_box_3 {
  .form_list {
    display: flex;
    flex-wrap: wrap;

    .list {
      margin-top: -1px;
      margin-right: -1px;
      display: flex;
      align-items: center;
      width: 50%;
      border: 1px solid $base-border-color;
      border-top: 1px solid $base-border-color;

      &.col-6 {
        width: 50%;
      }

      &.col-12 {
        width: 100%;
      }

      .clum {
        display: inline-flex;
        justify-content: flex-end;
        width: 140px;
        height: 100%;
        padding: 8px;
        background-color: $base-bg-color;
      }

      .text {
        display: inline-flex;
        flex-grow: 1;
        padding: 8px;
      }
    }
  }
}


// 详情
.detail-box {
  border: 1px solid $base-border-color;
  border-top: 1px solid $base-border-color;

  > div {
    .label {
      background-color: $base-bg-color;
      display: inline-flex;
      justify-content: flex-end;
      padding: 8px;
      align-items: center;
    }

    .ct {
      padding: 8px;
    }
  }
}

.box-title {
  display: flex;
  align-items: center;
  margin-top: 8px;
  font-size: 15px;

  &:before {
    display: inline-block;
    content: '';
    height: 14px;
    width: 3px;
    //background-color: $primary-color;
    margin-right: 5px;
  }

  .box_title_btn {
    margin-left: auto;
  }
}

.group-box {
  .el-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0 !important;
    border-left: 1px solid $base-border-color;
    border-top: 1px solid $base-border-color;

    + .el-row {
      border-top: 0 none;
    }

    .el-col {
      display: flex;

      border-right: 1px solid $base-border-color;
      border-bottom: 1px solid $base-border-color;
    }

    .el-form-item {
      //display: flex;
      //align-items: stretch;
      margin-bottom: 0;
      width: 100%;

      .el-form-item__label {
        display: inline;
        height: 100%;
        align-items: stretch;
        width: 150px;
        padding: 4px 8px;
        font-weight: bold;
        background-color: $base-bg-color;
      }

      .el-form-item__error {
        display: flex;
        position: static;
      }

      .el-form-item__content {
        display: flex;
        flex-wrap: wrap;
        align-items: stretch;
        justify-content: flex-start;
        margin-left: 0px !important;
        padding: 4px 8px;
        flex-direction: column;

        flex: 2;

        .el-radio {
          line-height: 32px;
        }
      }

      textarea {
        width: 100%;
      }
    }
  }
}

html,
body {
  touch-action: none; /* 禁用触摸事件 */
  overflow-x: hidden; /* 禁用横向滚动 */
}

// 元素全屏
.el-full-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100vw !important;
  height: 100% !important;
  z-index: 2300;
  margin-top: 0;
  padding: 15px;
  box-sizing: border-box;
  background-color: var(--art-main-bg-color);
  display: flex;
  flex-direction: column;
}

// 表格卡片
.art-table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 15px;
  border-radius: calc(var(--custom-radius) / 2 + 2px) !important;

  .el-card__body {
    height: 100%;
    overflow: hidden;
  }
}

// 容器全高
.art-full-height {
  height: var(--art-full-height);
  display: flex;
  flex-direction: column;

  @media (max-width: $device-phone) {
    height: auto;
  }
}

// 卡片盒子间距
.card-box {
  padding: 0 10px 20px 10px;

  @media (max-width: $device-phone) {
    padding: 0 5px 15px 5px;
  }
}

// 徽章样式
.art-badge {
  position: absolute;
  top: 0;
  right: 20px;
  bottom: 0;
  width: 6px;
  height: 6px;
  margin: auto;
  background: #ff3860;
  border-radius: 50%;
  animation: breathe 1.5s ease-in-out infinite;

  &.art-badge-horizontal {
    right: 0;
  }

  &.art-badge-mixed {
    right: 0;
  }

  &.art-badge-dual {
    right: 5px;
    top: 5px;
    bottom: auto;
  }
}

// 文字徽章样式
.art-text-badge {
  position: absolute;
  top: 0;
  right: 12px;
  bottom: 0;
  min-width: 20px;
  height: 18px;
  line-height: 17px;
  padding: 0 5px;
  margin: auto;
  font-size: 10px;
  color: #fff;
  text-align: center;
  background: #fd4e4e;
  border-radius: 4px;
}

@keyframes breathe {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.1);
  }

  100% {
    opacity: 0.7;
    transform: scale(1);
  }
}

// 修复老机型 loading 定位问题
.art-loading-fix {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.art-loading-fix .el-loading-spinner {
  position: static !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
}
