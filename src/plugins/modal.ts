import { ElLoading, ElMessageBox, ElNotification } from 'element-plus'
import { message } from '@/utils/resetMessage.js'
import { getLang } from '@/locales'

let loadingInstance

export default {
  // 消息提示
  msg(content) {
    message.info(content)
  },
  // 错误消息
  msgError(content) {
    message.error(content)
  },
  // 成功消息
  msgSuccess(content) {
    message.success(content)
  },
  // 警告消息
  msgWarning(content) {
    message.warning(content)
  },
  // 弹出提示
  alert(content) {
    ElMessageBox.alert(content, getLang('system.message.prompt'))
  },
  // 错误提示
  alertError(content) {
    ElMessageBox.alert(content, getLang('system.message.prompt'), { type: 'error' })
  },
  // 成功提示
  alertSuccess(content) {
    ElMessageBox.alert(content, getLang('system.message.prompt'), { type: 'success' })
  },
  // 警告提示
  alertWarning(content) {
    ElMessageBox.alert(content, getLang('system.message.prompt'), { type: 'warning' })
  },
  // 通知提示
  notify(content) {
    ElNotification.info(content)
  },
  // 错误通知
  notifyError(content) {
    ElNotification.error(content)
  },
  // 成功通知
  notifySuccess(content) {
    ElNotification.success(content)
  },
  // 警告通知
  notifyWarning(content) {
    ElNotification.warning(content)
  },
  // 确认窗体
  confirm(content) {
    return ElMessageBox.confirm(content, getLang('system.message.prompt'), {
      confirmButtonText: getLang('common.define'),
      cancelButtonText: getLang('common.cancel'),
      type: 'warning'
    })
  },
  // 提交内容
  prompt(content) {
    return ElMessageBox.prompt(content, getLang('system.message.prompt'), {
      confirmButtonText: getLang('common.define'),
      cancelButtonText: getLang('common.cancel'),
      type: 'warning'
    })
  },
  // 打开遮罩层
  loading(content) {
    loadingInstance = ElLoading.service({
      lock: true,
      text: content,
      background: 'rgba(0, 0, 0, 0.7)'
    })
  },
  // 关闭遮罩层
  closeLoading() {
    loadingInstance.close()
  }
}
