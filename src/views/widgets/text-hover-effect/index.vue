<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-2">
        文本悬停高亮效果
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        鼠标移动时跟随的文本高亮效果，支持自定义文本和颜色
      </p>
    </div>

    <!-- 示例区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 基础示例 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span class="font-semibold">基础示例</span>
          </div>
        </template>
        <div class="demo-container">
          <ArtTextHoverEffect 
            text="HOVER ME" 
            class="w-full h-32"
          />
        </div>
        <div class="demo-description">
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-4">
            将鼠标移动到文本上，观察彩色高亮效果跟随鼠标移动
          </p>
        </div>
      </el-card>

      <!-- 自定义文本示例 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span class="font-semibold">自定义文本</span>
          </div>
        </template>
        <div class="demo-container">
          <ArtTextHoverEffect 
            text="ART DESIGN" 
            class="w-full h-32"
          />
        </div>
        <div class="demo-description">
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-4">
            自定义显示的文本内容
          </p>
        </div>
      </el-card>

      <!-- 自定义颜色示例 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span class="font-semibold">自定义颜色</span>
          </div>
        </template>
        <div class="demo-container">
          <ArtTextHoverEffect 
            text="COLORFUL" 
            :colors="customColors"
            class="w-full h-32"
          />
        </div>
        <div class="demo-description">
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-4">
            使用自定义的渐变颜色组合
          </p>
        </div>
      </el-card>

      <!-- 大尺寸示例 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span class="font-semibold">大尺寸文本</span>
          </div>
        </template>
        <div class="demo-container">
          <ArtTextHoverEffect 
            text="BIG" 
            fontSize="text-8xl"
            class="w-full h-40"
          />
        </div>
        <div class="demo-description">
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-4">
            更大的文本尺寸展示
          </p>
        </div>
      </el-card>
    </div>

    <!-- 全屏展示区域 -->
    <el-card class="demo-card mt-6">
      <template #header>
        <div class="card-header">
          <span class="font-semibold">全屏展示</span>
          <el-button 
            type="primary" 
            size="small"
            @click="toggleFullscreen"
          >
            {{ isFullscreen ? '退出全屏' : '全屏展示' }}
          </el-button>
        </div>
      </template>
      <div 
        ref="fullscreenContainer"
        :class="[
          'demo-container transition-all duration-300',
          isFullscreen ? 'fixed inset-0 z-50 bg-black' : 'h-64'
        ]"
      >
        <ArtTextHoverEffect 
          :text="fullscreenText" 
          :colors="fullscreenColors"
          class="w-full h-full"
          fontSize="text-9xl"
        />
      </div>
      <div v-if="!isFullscreen" class="demo-description">
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-4">
          点击全屏按钮体验完整效果，按 ESC 键退出全屏
        </p>
      </div>
    </el-card>

    <!-- 使用说明 -->
    <el-card class="demo-card mt-6">
      <template #header>
        <div class="card-header">
          <span class="font-semibold">使用说明</span>
        </div>
      </template>
      <div class="usage-guide">
        <h3 class="text-lg font-semibold mb-3">组件属性</h3>
        <el-table :data="propsData" border>
          <el-table-column prop="prop" label="属性名" width="150" />
          <el-table-column prop="type" label="类型" width="120" />
          <el-table-column prop="default" label="默认值" width="150" />
          <el-table-column prop="description" label="说明" />
        </el-table>

        <h3 class="text-lg font-semibold mt-6 mb-3">基础用法</h3>
        <div v-highlight>
          <pre><code class="language-vue">{{ basicUsageCode }}</code></pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ArtTextHoverEffect from '@/components/core/text-effect/art-text-hover-effect/index.vue'

// 响应式数据
const isFullscreen = ref(false)
const fullscreenContainer = ref<HTMLElement>()

// 自定义颜色配置
const customColors = ['#ff6b6b', '#feca57', '#48dbfb', '#1dd1a1', '#ff9ff3']
const fullscreenColors = ['#ff0080', '#ff8c00', '#00ff80', '#0080ff', '#8000ff']
const fullscreenText = 'AMAZING'

// 属性说明数据
const propsData = [
  {
    prop: 'text',
    type: 'String',
    default: 'HOVER',
    description: '显示的文本内容'
  },
  {
    prop: 'colors',
    type: 'Array',
    default: '5种默认颜色',
    description: '渐变颜色数组，支持5种颜色'
  },
  {
    prop: 'fontSize',
    type: 'String',
    default: 'text-7xl',
    description: 'Tailwind CSS 字体大小类名'
  },
  {
    prop: 'viewBox',
    type: 'String',
    default: '0 0 300 100',
    description: 'SVG viewBox 属性'
  },
  {
    prop: 'duration',
    type: 'Number',
    default: '0',
    description: '动画持续时间（毫秒）'
  }
]

// 基础用法代码
const basicUsageCode = `<template>
  <ArtTextHoverEffect 
    text="YOUR TEXT" 
    :colors="['#eab308', '#ef4444', '#3b82f6', '#06b6d4', '#8b5cf6']"
    fontSize="text-7xl"
    class="w-full h-32"
  />
</template>

<script setup>
import ArtTextHoverEffect from '@/components/core/text-effect/art-text-hover-effect/index.vue'
</script>`

// 全屏功能
const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    enterFullscreen()
  } else {
    exitFullscreen()
  }
}

const enterFullscreen = () => {
  isFullscreen.value = true
  document.body.style.overflow = 'hidden'
}

const exitFullscreen = () => {
  isFullscreen.value = false
  document.body.style.overflow = ''
}

// 键盘事件处理
const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && isFullscreen.value) {
    exitFullscreen()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  if (isFullscreen.value) {
    exitFullscreen()
  }
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.demo-card {
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 6px;
  min-height: 120px;
}

.dark .demo-container {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.demo-description {
  margin-top: 16px;
}

.usage-guide {
  padding: 16px 0;
}

/* 全屏样式 */
.demo-container.fixed {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}
</style>
