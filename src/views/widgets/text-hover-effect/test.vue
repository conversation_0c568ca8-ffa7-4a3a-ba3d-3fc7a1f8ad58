<template>
  <div class="test-container">
    <h1>文本悬停效果测试</h1>
    <div class="demo-area">
      <ArtTextHoverEffect 
        text="HOVER ME" 
        class="w-full h-32"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import ArtTextHoverEffect from '@/components/core/text-effect/art-text-hover-effect/index.vue'
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.demo-area {
  margin-top: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 20px;
}
</style>
