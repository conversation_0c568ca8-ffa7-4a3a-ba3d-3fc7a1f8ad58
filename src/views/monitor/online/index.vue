<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />
    <art-table
      v-loading="loading"
      :data="onlineList.slice((pageNum - 1) * pageSize, pageNum * pageSize)"
      :page-num="pageNum"
      :page-size="pageSize"
      :total="total"
      @search="getList"
      :add-button-row-height="true"
    >
      <el-table-column :label="$t('number.serial')" align="center" type="index" width="140">
        <template #default="scope">
          <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('number.session')"
        :show-overflow-tooltip="true"
        align="center"
        prop="tokenId"
      />
      <el-table-column
        :label="$t('name.login')"
        :show-overflow-tooltip="true"
        align="center"
        prop="userName"
      />
      <el-table-column
        :label="$t('department.affiliated')"
        :show-overflow-tooltip="true"
        align="center"
        prop="deptName"
      />
      <el-table-column
        :label="$t('host')"
        :show-overflow-tooltip="true"
        align="center"
        prop="ipaddr"
      />
      <el-table-column
        :label="$t('location.login')"
        :show-overflow-tooltip="true"
        align="center"
        prop="loginLocation"
      />
      <el-table-column
        :label="$t('system.operating')"
        :show-overflow-tooltip="true"
        align="center"
        prop="os"
      />
      <el-table-column
        :label="$t('browser')"
        :show-overflow-tooltip="true"
        align="center"
        prop="browser"
      />
      <el-table-column :label="$t('time.login')" align="center" prop="loginTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.loginTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['monitor:online:forceLogout']"
            icon="Delete"
            link
            type="danger"
            @click="handleForceLogout(scope.row)"
          >
            {{ $t('logout.force') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>
  </div>
</template>

<script name="Online" setup>
  import { getCurrentInstance } from 'vue'
  import { forceLogout, list as initData } from '@/api/monitor/online'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'

  const { proxy } = getCurrentInstance()

  const onlineList = ref([])
  const loading = ref(true)
  const total = ref(0)
  const pageNum = ref(1)
  const pageSize = ref(10)

  const data = reactive({
    fromKey: 'online',
    fromName: 'online',
    queryParams: {
      ipaddr: undefined,
      userName: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询在线用户列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    initData(queryParams.value).then((response) => {
      onlineList.value = response.data
      total.value = response.data.length
      loading.value = false
    })
  }

  /** 强退按钮操作 */
  function handleForceLogout(row) {
    proxy.$modal
      .confirm(proxy.$t('confirm.logout.force.name') + row.userName + proxy.$t('user.question'))
      .then(function () {
        return forceLogout(row.tokenId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>
