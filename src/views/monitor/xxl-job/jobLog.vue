<!--任务管理-->
<template>
  <div class="page-content">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item :label="$t('executor')" prop="jobGroup">
        <el-select
          v-model="queryParams.jobGroup"
          :placeholder="$t('executor.select')"
          filterable
          style="width: 200px"
          @change="execteChange"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('task')" prop="jobId">
        <el-select
          v-model="queryParams.jobId"
          :placeholder="$t('task.select')"
          style="width: 200px"
          @change="taskChange"
        >
          <el-option
            v-for="item in jobList"
            :key="item.id"
            :label="item.jobDesc"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('status')" prop="logStatus">
        <el-select
          v-model="queryParams.logStatus"
          :placeholder="$t('status.select')"
          style="width: 200px"
          @change="getList"
        >
          <el-option
            v-for="item in dispatch_log"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('time.scheduling')" prop="logStatus">
        <DateSelect ref="time" @change="timeChange" />
      </el-form-item>

      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">{{
          $t('action.search')
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="Delete" plain type="danger" @click="clearVisible = true"
          >{{ $t('cleanup.log') }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--<el-table v-loading="loading" :data="logList">-->
    <art-table
      v-loading="loading"
      :data="logList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="getList"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('id.task')" align="center" prop="jobId">
        <template #default="scope">
          <el-popover
            :title="$t('information.execution')"
            :width="300"
            placement="right"
            trigger="click"
          >
            <template #default>
              <div>
                <p>{{ $t('address.executor') }}</p>
                <p>JobHandler：{{ scope.row.executorHandler }}</p>
                <p>{{ $t('parameters.task') }}</p>
              </div>
            </template>
            <template #reference>
              <el-button type="text">
                {{ scope.row.jobId }}
              </el-button>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column :label="$t('time.scheduling')" align="center" prop="triggerTime">
        <template #default="scope">
          {{ parseTime(scope.row.triggerTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('result.scheduling')" align="center" prop="triggerCode">
        <template #default="scope">
          <el-tag v-if="scope.row.triggerCode === 200" type="success">{{
            $t('status.success')
          }}</el-tag>
          <el-tag v-else type="danger">{{ $t('status.failed') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('remarks.scheduling')" align="center" prop="triggerMsg">
        <template #default="scope">
          <el-popover
            :title="$t('remarks.scheduling')"
            :width="400"
            placement="right"
            trigger="click"
          >
            <template #default>
              <div style="text-align: center" v-html="scope.row.triggerMsg"> </div>
            </template>
            <template #reference>
              <el-button type="text">{{ $t('action.view') }}</el-button>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column :label="$t('time.execution')" align="center" prop="handleTime">
        <template #default="scope">
          {{ parseTime(scope.row.handleTime) }}
        </template>
      </el-table-column>

      <el-table-column :label="$t('result.execution')" align="center" prop="handleCode">
        <template #default="scope">
          <el-tag v-if="scope.row.handleCode === 200" type="success">{{
            $t('status.success')
          }}</el-tag>
          <el-tag v-else-if="scope.row.handleCode === 500" type="danger">{{
            $t('status.failed')
          }}</el-tag>
          <el-tag v-else-if="scope.row.handleCode === 502" type="danger">{{
            $t('status.failed.timeout')
          }}</el-tag>
          <span v-else></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('remarks.execution')" align="center" prop="handleMsg">
        <template #default="scope">
          <span v-if="scope.row.handleMsg === '' || scope.row.handleMsg === null">{{
            $t('none')
          }}</span>
          <el-tooltip v-else :content="scope.row.handleMsg" class="box-item" placement="left-start">
            <el-button type="text">{{ $t('action.view') }}</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-dropdown v-if="scope.row.triggerCode !== 500" @command="more">
            <el-button type="primary"
              >{{ $t('operation') }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{ type: 'log', id: scope.row.id }">{{
                  $t('log.execution')
                }}</el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.handleCode === 0"
                  :command="{ type: 'stop', id: scope.row.id }"
                  divided
                >
                  $t('task.terminate')
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </art-table>

    <!--日志清理-->

    <el-dialog v-model="clearVisible" :title="$t('cleanup.log')" append-to-body width="25%">
      <el-form :model="clearForm">
        <el-form-item :label="$t('executor')" prop="type" style="width: 100% !important">
          <el-select
            v-model="clearForm.jobGroup"
            :placeholder="$t('executor.select')"
            disabled
            style="width: 100% !important"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('task')" prop="type" style="width: 100% !important">
          <el-select
            v-model="clearForm.jobId"
            :placeholder="$t('task.select')"
            disabled
            style="width: 100% !important"
          >
            <el-option
              v-for="item in jobList"
              :key="item.id"
              :label="item.jobDesc"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('method.cleanup')" prop="type" style="width: 100% !important">
          <el-select
            v-model="clearForm.type"
            :placeholder="$t('executor.select')"
            filterable
            style="width: 100% !important"
          >
            <el-option
              v-for="item in clear_log"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="logClear">{{ $t('action.confirm') }}</el-button>
          <el-button @click="clearVisible = false">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="logVisible"
      :show-close="false"
      :title="$t('log.execution')"
      fullscreen
      width="50%"
    >
      <template #title>
        <logConsole :content="logContent" @close="logVisible = false" @refresh="refresh" />
      </template>
    </el-dialog>
  </div>
</template>

<script name="Job_log" setup>
  import { getCurrentInstance } from 'vue'
  import { clearLog, getById, getJobsByGroup, getLogContent, list, stopLog } from '@/api/job/jobLog'

  import { executeHandler } from '@/api/job/jobInfo'
  import DateSelect from '@/components/DateSelect/DateSelect'
  import logConsole from './logConsole'

  const { proxy } = getCurrentInstance()

  const logList = ref([])
  const open = ref(false)
  const clearVisible = ref(false)
  const logVisible = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])

  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const options = ref([
    {
      value: 0,
      label: proxy.$t('all')
    }
  ])
  let arr = [
    {
      id: 0,
      jobDesc: proxy.$t('all')
    }
  ]

  const { dispatch_log, clear_log } = proxy.useDict('dispatch_log', 'clear_log')

  const data = reactive({
    logContent: '',
    logInfo: {},
    queryParams: {
      start: 1,
      length: 10,
      filterTime: '',
      jobGroup: '', //执行器
      jobId: 0, //任务ID
      logStatus: '-1' //日志状态
    },
    clearForm: {
      jobGroup: '',
      jobGroupName: '',
      jobId: 0,
      jobIdName: '',
      type: '1'
    },
    jobList: [
      {
        id: 0,
        jobDesc: proxy.$t('all')
      }
    ]
  })

  const { queryParams, jobList, clearForm, logContent, logInfo } = toRefs(data)

  function taskChange(val) {
    clearForm.value.jobId = val
    getList()
  }

  /**
   * 调度时间改变
   * @param val
   */
  function timeChange(val) {
    if (!val) {
      queryParams.value.filterTime = ''
    } else {
      queryParams.value.filterTime = val.startDate + ' - ' + val.endDate
    }
    queryParams.value.jobGroup = options.value[0].value
    getList()
  }

  /**
   * 刷新日志
   */
  function refresh() {
    getLogContent(logInfo.value).then((res) => {
      if (res.code === 200) {
        let content = res.content.logContent
        logContent.value = content
        logVisible.value = true
        return
      }
      proxy.$modal.msgError(res.msg)
    })
  }

  /**
   * 停止任务
   * @param id
   */
  function stopLogHandle(id) {
    proxy.$modal.confirm(proxy.$t('confirm.stop.task')).then((res) => {
      stopLog({ id: id }).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess(proxy.$t('operation.success'))
          getList()
          return
        }
        proxy.$modal.msgError(res.msg)
      })
    })
  }

  /**
   * 日志清理
   */
  function logClear() {
    clearLog(clearForm.value).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess(proxy.$t('operation.success'))
        clearVisible.value = false
        getList()
        return
      }
      proxy.$modal.msgError(res.msg)
    })
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.start = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    queryParams.value.filterTime = ''
    proxy.$refs.time.clear()
    queryParams.value.jobGroup = options.value[0].value

    handleQuery()
  }

  /**
   * 操作
   */
  function more(val) {
    switch (val.type) {
      case 'log':
        getLogInfo(val.id)
        break
      case 'stop':
        stopLogHandle(val.id)
        break
    }
  }

  /**
   * 获取日志内容
   * @param id
   */
  function getLogInfo(id) {
    getById({ id }).then((res) => {
      if (res.code === 200) {
        let value = res.data
        value.fromLineNum = 1
        logInfo.value = value
        refresh()
      }
    })
  }

  /**
   * 获取执行器列表
   */
  function getExecuteHandler() {
    executeHandler()
      .then((res) => {
        let isAdmin = localStorage.getItem('isAdmin')
        if (isAdmin === 'false') {
          options.value = res.data
          queryParams.value.jobGroup = res.data[0].value
        } else {
          options.value = options.value.concat(res.data)
          queryParams.value.jobGroup = 0
        }
        getList()
      })
      .catch((err) => {
        queryParams.value.jobGroup = 0
        loading.value = false
      })
  }

  /**
   * 执行改变
   */
  function execteChange(val) {
    clearForm.value.jobGroup = val
    //执行器改变后，任务重置为全部
    queryParams.value.jobId = 0
    getJobsByGroup({ jobGroup: val }).then((res) => {
      jobList.value = arr.concat(res.content)
    })
    getList()
  }

  /**
   * 列表
   */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    let query = proxy.$route.query
    if (Object.keys(query).length > 0) {
      getJobsByGroup({ jobGroup: query.jobGroup }).then((res) => {
        jobList.value = arr.concat(res.content)
      })
      queryParams.value.jobGroup = parseInt(query.jobGroup)
      queryParams.value.jobId = parseInt(query.id)
    }
    loading.value = true
    list(queryParams.value).then((response) => {
      logList.value = response.data
      total.value = response.recordsFiltered
    })
    loading.value = false
  }

  getExecuteHandler()
</script>

<style scoped>
  :deep(.el-dialog__header) {
    padding: 0 !important;
    padding-bottom: 0 !important;
    margin-right: 0 !important;
    word-break: break-all;
  }
</style>
