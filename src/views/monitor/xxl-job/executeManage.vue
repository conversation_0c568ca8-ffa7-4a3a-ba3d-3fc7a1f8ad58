<!--任务管理-->
<template>
  <div class="page-content">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item label="AppName" prop="appname">
        <el-input v-model="queryParams.appname" />
      </el-form-item>
      <el-form-item :label="$t('name')" prop="title">
        <el-input v-model="queryParams.title" />
      </el-form-item>

      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">{{
          $t('action.search')
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="Plus" plain type="primary" @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!--<el-table v-loading="loading" :data="logList">-->
    <art-table
      v-loading="loading"
      :data="logList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="getList"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="AppName" prop="appname" />
      <el-table-column :label="$t('name')" align="center" prop="title" />
      <el-table-column :label="$t('method.registration')" align="center" prop="addressType">
        <template #default="scope">
          <span v-if="scope.row.addressType === 0">{{ $t('registration.automatic') }}</span>
          <span v-else>{{ $t('entry.manual') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('address.machine.online')" align="center" prop="triggerMsg">
        <template #default="scope">
          <el-popover
            v-if="scope.row.registryList !== null"
            :title="$t('address.machine')"
            :width="400"
            placement="right"
            trigger="click"
          >
            <template #default>
              <el-tag v-for="(item, index) in scope.row.registryList" :key="index" type="success">{{
                item
              }}</el-tag>
            </template>
            <template #reference>
              <el-button type="text">{{ $t('action.view') }}</el-button>
            </template>
          </el-popover>
          <span v-else>{{ $t('none') }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-dropdown v-if="scope.row.triggerCode !== 500" @command="more">
            <el-button type="primary"
              >{{ $t('operation') }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{ type: 'edit', id: scope.row.id }">{{
                  $t('action.edit')
                }}</el-dropdown-item>
                <el-dropdown-item :command="{ type: 'remove', id: scope.row.id }"
                  >{{ $t('action.delete') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </art-table>

    <!--新增执行器-->

    <el-dialog v-model="open" :title="title" append-to-body width="25%" @close="dialogClose">
      <el-form ref="form" :model="dataInfo" :rules="rules" label-width="130px">
        <el-form-item :label="$t('name.app.input')" prop="appname" style="width: 100% !important">
          <el-input v-model="dataInfo.appname" :placeholder="$t('name.app.input')" />
        </el-form-item>
        <el-form-item :label="$t('name')" prop="title" style="width: 100% !important">
          <el-input v-model="dataInfo.title" :placeholder="$t('name.input')" />
        </el-form-item>
        <el-form-item
          :label="$t('method.registration')"
          prop="addressType"
          style="width: 100% !important"
        >
          <el-radio-group v-model="dataInfo.addressType">
            <el-radio v-for="item in regType" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :label="$t('address.machine')"
          :prop="dataInfo.addressType === '1' ? 'addressList' : ''"
          style="width: 100% !important"
        >
          <el-input
            v-model="dataInfo.addressList"
            :disabled="dataInfo.addressType === 0"
            :placeholder="$t('address.machine.input')"
            resize="none"
            type="textarea"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submit">{{ $t('action.confirm') }}</el-button>
          <el-button @click="open = false">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="Execute_manage" setup>
  import { getCurrentInstance } from 'vue'
  import { getById, list, remove, save } from '@/api/job/jobGroup'

  const { proxy } = getCurrentInstance()

  const logList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])

  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    regType: [
      { value: 0, label: proxy.$t('registration.automatic') },
      { value: 1, label: proxy.$t('entry.manual') }
    ],
    dataInfo: {
      appname: '',
      title: '',
      addressType: 0,
      addressList: ''
    },
    queryParams: {
      start: 1,
      length: 10,
      appname: '',
      title: ''
    },
    rules: {
      appname: [
        { required: true, message: proxy.$t('name.app.input'), trigger: 'blur' },
        { min: 4, max: 20, message: proxy.$t('length.characters.between'), trigger: 'blur' }
      ],
      title: [{ required: true, message: proxy.$t('name.input'), trigger: 'blur' }],
      addressType: [
        { required: true, message: proxy.$t('method.selection.input'), trigger: 'blur' }
      ],
      addressList: [{ required: true, message: proxy.$t('address.machine.input'), trigger: 'blur' }]
    }
  })

  const { queryParams, dataInfo, rules, regType } = toRefs(data)

  function dialogClose() {
    dataInfo.value = {
      appname: '',
      title: '',
      addressType: 0,
      addressList: ''
    }
  }

  /**
   * 保存
   */
  function submit() {
    proxy.$refs['form'].validate(async (valid) => {
      if (valid) {
        save(dataInfo.value).then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess(proxy.$t('save.success'))
            open.value = false
            getList()

            return
          }
          proxy.$modal.msgError(res.message)
        })
      }
    })
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.start = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    handleQuery()
  }

  /**
   * 显示新增页面
   */
  function handleAdd() {
    title.value = proxy.$t('executor.add')
    open.value = true
  }

  /**
   * 操作
   */
  function more(val) {
    switch (val.type) {
      case 'edit':
        showEditDialo(val.id)
        break
      case 'remove':
        removeExecute(val.id)
        break
    }
  }

  function showEditDialo(id) {
    getById({ id }).then((res) => {
      if (res.code === 200) {
        dataInfo.value = res.data
        title.value = proxy.$t('executor.edit')
        open.value = true
        return
      }
      proxy.$modal.msgError(res.message)
    })
  }

  function removeExecute(id) {
    proxy.$modal.confirm(proxy.$t('executor.delete.confirm')).then((res) => {
      remove({ id: id }).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess(proxy.$t('operation.success'))
          getList()
          return
        }
        proxy.$modal.msgError(proxy.$t('operation.fail'))
      })
    })
  }

  /**
   * 列表
   */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    list(queryParams.value).then((response) => {
      logList.value = response.data
      total.value = response.recordsFiltered
      loading.value = false
    })
  }

  getList()
</script>

<style scoped>
  :deep(.el-dialog__header) {
    padding: 0 !important;
    padding-bottom: 0 !important;
    margin-right: 0 !important;
    word-break: break-all;
  }
</style>
