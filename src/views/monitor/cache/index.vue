<template>
  <div class="">
    <el-row :gutter="0" style="margin: 0 -10px;">
      <el-col :span="24" class="card-box">
        <el-card>
          <template #header>
            <Monitor style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('information.basic') }}</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%">
              <tbody>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('version.redis') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.redis_version }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('mode.operating') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.redis_mode == 'standalone' ? '单机' : '集群' }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('port') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.tcp_port }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('clients.number') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.connected_clients }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('time.running.days') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.uptime_in_days }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('memory.using') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.used_memory_human }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('cpu.using') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('configuration.memory') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.maxmemory_human }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('aof.enabled') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.aof_enabled == '0' ? '否' : '是' }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('rdb.success') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.rdb_last_bgsave_status }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('key.quantity') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.dbSize" class="cell">{{ cache.dbSize }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('network.ingress.egress') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.instantaneous_input_kbps }}kps/{{
                        cache.info.instantaneous_output_kbps
                      }}kps
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <PieChart style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('statistics.command') }}</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="commandstats" style="height: 420px" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <Odometer style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('memory.info') }}</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="usedmemory" style="height: 420px" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script name="Cache" setup>
  import { getCache } from '@/api/monitor/cache'
  import * as echarts from 'echarts'
  import { getCurrentInstance } from 'vue'
  import { useChart } from '@/composables/useChart'
  import { useSettingStore } from '@/store/modules/setting'
  import { storeToRefs } from 'pinia'

  const { proxy } = getCurrentInstance()
  const settingStore = useSettingStore()
  const { isDark } = storeToRefs(settingStore)

  const cache = ref([])
  const commandstats = ref(null)
  const usedmemory = ref(null)

  let commandstatsChart = null
  let usedmemoryChart = null

  // 获取主题相关的颜色配置
  const getThemeColors = () => {
    const textColor = isDark.value ? '#ffffff' : '#333333'
    const axisLineColor = isDark.value ? '#484753' : '#e0e6f1'
    const splitLineColor = isDark.value ? '#484753' : '#e0e6f1'

    return {
      textColor,
      axisLineColor,
      splitLineColor
    }
  }

  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    proxy.$modal.loading(proxy.$t('data.monitoring.cache.loading'))
    getCache().then((response) => {
      proxy.$modal.closeLoading()
      cache.value = response.data

      const { textColor } = getThemeColors()

      // 销毁之前的图表实例
      if (commandstatsChart) {
        commandstatsChart.dispose()
      }
      if (usedmemoryChart) {
        usedmemoryChart.dispose()
      }

      // 创建命令统计饼图
      commandstatsChart = echarts.init(commandstats.value)
      commandstatsChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)',
          backgroundColor: isDark.value ? '#2a2a2a' : '#ffffff',
          borderColor: isDark.value ? '#484753' : '#e0e6f1',
          textStyle: {
            color: textColor
          }
        },
        series: [
          {
            name: proxy.$t('command'),
            type: 'pie',
            roseType: 'radius',
            radius: [15, 95],
            center: ['50%', '38%'],
            data: response.data.commandStats,
            animationEasing: 'cubicInOut',
            animationDuration: 1000,
            label: {
              color: textColor
            },
            labelLine: {
              lineStyle: {
                color: textColor
              }
            }
          }
        ]
      })

      // 创建内存使用仪表盘
      usedmemoryChart = echarts.init(usedmemory.value)

      // 计算内存使用百分比（假设最大内存为配置的最大内存）
      const usedMemoryValue = parseFloat(cache.value.info.used_memory_human)
      const maxMemoryValue = parseFloat(cache.value.info.maxmemory_human) || 1000
      const memoryPercentage = Math.min((usedMemoryValue / maxMemoryValue) * 100, 100)

      usedmemoryChart.setOption({
        tooltip: {
          formatter: '{b} <br/>{a} : ' + cache.value.info.used_memory_human,
          backgroundColor: isDark.value ? '#2a2a2a' : '#ffffff',
          borderColor: isDark.value ? '#484753' : '#e0e6f1',
          textStyle: {
            color: textColor
          }
        },
        series: [
          {
            name: proxy.$t('peak'),
            type: 'gauge',
            min: 0,
            max: 100,
            radius: '75%',
            center: ['50%', '60%'],
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                width: 10,
                color: [
                  [0.3, '#67e0e3'],
                  [0.7, '#37a2da'],
                  [1, '#fd666d']
                ]
              }
            },
            axisLabel: {
              color: textColor,
              fontSize: 12,
              formatter: '{value}%'
            },
            axisTick: {
              length: 8,
              lineStyle: {
                color: textColor,
                width: 1
              }
            },
            splitLine: {
              length: 12,
              lineStyle: {
                color: textColor,
                width: 2
              }
            },
            pointer: {
              length: '60%',
              width: 4,
              itemStyle: {
                color: isDark.value ? '#ffffff' : '#37a2da'
              }
            },
            title: {
              show: true,
              offsetCenter: [0, '30%'],
              textStyle: {
                color: textColor,
                fontSize: 14
              }
            },
            detail: {
              valueAnimation: true,
              formatter: cache.value.info.used_memory_human,
              color: isDark.value ? '#ffffff' : '#333333',
              fontSize: 20,
              fontWeight: 'bold',
              offsetCenter: [0, '10%']
            },
            data: [
              {
                value: memoryPercentage,
                name: proxy.$t('consumption.memory')
              }
            ]
          }
        ]
      })

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (commandstatsChart) {
          commandstatsChart.resize()
        }
        if (usedmemoryChart) {
          usedmemoryChart.resize()
        }
      })
    })
  }

  // 监听主题变化，重新渲染图表
  watch(isDark, () => {
    if (cache.value && cache.value.info) {
      // 延迟一点时间让主题切换完成
      setTimeout(() => {
        getList()
      }, 100)
    }
  })

  // 组件销毁时清理图表实例
  onUnmounted(() => {
    if (commandstatsChart) {
      commandstatsChart.dispose()
      commandstatsChart = null
    }
    if (usedmemoryChart) {
      usedmemoryChart.dispose()
      usedmemoryChart = null
    }
  })

  getList()
</script>
