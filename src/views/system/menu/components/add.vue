<template>
  <Drawer v-model="open" @open="init" :size="700">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm">
          {{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="menuRef" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <!-- 上级菜单 -->
            <el-col :span="24">
              <el-form-item :label="$t('menu.parent')">
                <el-tree-select
                  v-model="form.parentId"
                  :data="menuOptions"
                  :placeholder="$t('menu.parent.select')"
                  :props="{ value: 'menuId', label: 'menuName', children: 'children' }"
                  check-strictly
                  value-key="menuId"
                >
                  <template #default="{ data: { menuName } }">
                    {{ $t(menuName) }}
                  </template>
                </el-tree-select>
              </el-form-item>
            </el-col>

            <!-- 菜单类型 -->
            <el-col :span="24">
              <el-form-item :label="$t('type.menu')" prop="menuType">
                <el-radio-group v-model="form.menuType">
                  <el-radio value="M">{{ $t('directory') }}</el-radio>
                  <el-radio value="C">{{ $t('menu') }}</el-radio>
                  <el-radio value="F">{{ $t('button') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- 菜单名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.menu')" prop="menuName">
                <el-input v-model="form.menuName" :placeholder="$t('name.menu.input')" />
              </el-form-item>
            </el-col>

            <!-- 显示排序 -->
            <el-col :span="12">
              <el-form-item :label="$t('sort.display')" prop="orderNum">
                <el-input-number
                  v-model="form.orderNum"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <!-- 菜单图标 -->
            <el-col v-if="form.menuType !== 'F'" :span="12">
              <el-form-item :label="$t('icon.menu')" prop="icon">
                <el-input
                  :placeholder="$t('icon.select.click')"
                  readonly
                  v-model="form.icon"
                  @click="showIcon = !showIcon"
                >
                  <template #prepend v-if="form.icon">
                    <i style="font-size: 22px" :class="`iconfont-sys ${form.icon}`"></i>
                  </template>
                </el-input>

                <ArtIconSelector
                  :iconType="IconTypeEnum.CLASS_NAME"
                  @getIcon="selected"
                  width="100%"
                  :defaultIcon="form.icon"
                  @dialogClose="showIcon = false"
                  v-if="showIcon"
                >
                </ArtIconSelector>
              </el-form-item>
            </el-col>

            <!-- 路由名称 -->
            <el-col v-if="form.menuType === 'C'" :span="12">
              <el-form-item prop="routeName">
                <template #label>
                  <span>
                    <el-tooltip :content="$t('route.address.default')" placement="top">
                      <el-icon><question-filled /></el-icon>
                    </el-tooltip>
                    {{ $t('name.route') }}
                  </span>
                </template>
                <el-input v-model="form.routeName" :placeholder="$t('name.route.input')" />
              </el-form-item>
            </el-col>

            <!-- 是否外链 -->
            <el-col v-if="form.menuType != 'F'" :span="12">
              <el-form-item>
                <template #label>
                  <span>
                    <el-tooltip :content="$t('route.address.external')" placement="top">
                      <el-icon><question-filled /></el-icon>
                    </el-tooltip>
                    {{ $t('link.external.question') }}
                  </span>
                </template>
                <el-radio-group v-model="form.isFrame">
                  <el-radio value="0">{{ $t('yes') }}</el-radio>
                  <el-radio value="1">{{ $t('no') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- 路由地址 -->
            <el-col v-if="form.menuType != 'F'" :span="12">
              <el-form-item prop="path">
                <template #label>
                  <span>
                    <el-tooltip :content="$t('route.address.access')" placement="top">
                      <el-icon><question-filled /></el-icon>
                    </el-tooltip>
                    {{ $t('address.route') }}
                  </span>
                </template>
                <el-input v-model="form.path" :placeholder="$t('address.route.input')" />
              </el-form-item>
            </el-col>

            <!-- 组件路径 -->
            <el-col v-if="form.menuType === 'C'" :span="12">
              <el-form-item prop="component">
                <template #label>
                  <span>
                    <el-tooltip :content="$t('path.component.access')" placement="top">
                      <el-icon><question-filled /></el-icon>
                    </el-tooltip>
                    {{ $t('path.component') }}
                  </span>
                </template>
                <el-input v-model="form.component" :placeholder="$t('path.component.input')" />
              </el-form-item>
            </el-col>

            <!-- 权限标识 -->
            <el-col v-if="form.menuType != 'M'" :span="12">
              <el-form-item>
                <template #label>
                  <span>
                    <el-tooltip :content="$t('character.permission.controller')" placement="top">
                      <el-icon><question-filled /></el-icon>
                    </el-tooltip>
                    {{ $t('character.permission') }}
                  </span>
                </template>
                <el-input
                  v-model="form.perms"
                  :placeholder="$t('identifier.permission.input')"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>

            <!-- 路由参数 -->
            <el-col v-if="form.menuType === 'C'" :span="12">
              <el-form-item>
                <template #label>
                  <span>
                    <el-tooltip :content="$t('parameters.route.default')" placement="top">
                      <el-icon><question-filled /></el-icon>
                    </el-tooltip>
                    {{ $t('parameters.route') }}
                  </span>
                </template>
                <el-input
                  v-model="form.query"
                  :placeholder="$t('parameters.route.input')"
                  maxlength="255"
                />
              </el-form-item>
            </el-col>
            <!-- 是否缓存 -->
            <el-col v-if="form.menuType === 'C'" :span="12">
              <el-form-item>
                <template #label>
                  <span>
                    <el-tooltip :content="$t('cache.keep.alive')" placement="top">
                      <el-icon><question-filled /></el-icon>
                    </el-tooltip>
                    {{ $t('cache.question') }}
                  </span>
                </template>
                <el-radio-group v-model="form.isCache">
                  <el-radio value="0">{{ $t('cache') }}</el-radio>
                  <el-radio value="1">{{ $t('cache.not') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- 显示状态 -->
            <el-col v-if="form.menuType != 'F'" :span="12">
              <el-form-item>
                <template #label>
                  <span>
                    <el-tooltip :content="$t('route.hide.select')" placement="top">
                      <el-icon><question-filled /></el-icon>
                    </el-tooltip>
                    {{ $t('status.display') }}
                  </span>
                </template>
                <el-radio-group v-model="form.visible">
                  <el-radio v-for="dict in sys_show_hide" :key="dict.value" :value="dict.value">
                    {{ $t(dict.type + '.' + dict.value) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <!-- 菜单状态 -->
            <el-col :span="12">
              <el-form-item>
                <template #label>
                  <span>
                    <el-tooltip :content="$t('route.disable.select')" placement="top">
                      <el-icon><question-filled /></el-icon>
                    </el-tooltip>
                    {{ $t('status.menu') }}
                  </span>
                </template>
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in sys_normal_disable"
                    :key="dict.value"
                    :value="dict.value"
                  >
                    {{ $t(dict.type + '.' + dict.value) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { addMenu, getMenu, listChildren, updateMenu } from '@/api/system/menu'
  import { IconTypeEnum } from '@/enums/appEnum'
  import ArtIconSelector from '@/components/core/base/art-icon-selector/index.vue'

  const { proxy } = getCurrentInstance()
  const { sys_show_hide, sys_normal_disable } = proxy.useDict('sys_show_hide', 'sys_normal_disable')

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    },
    parentId: {
      type: [String, Number],
      default: 0
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    rules: {
      menuName: [{ required: true, message: proxy.$t('menu.name.empty'), trigger: 'blur' }],
      orderNum: [{ required: true, message: proxy.$t('sequence.menu.empty'), trigger: 'blur' }],
      path: [{ required: true, message: proxy.$t('address.route.empty'), trigger: 'blur' }]
    },
    menuOptions: [],
    showIcon: false
  })

  const { form, rules, menuOptions, showIcon } = toRefs(data)

  const loadingBtn = reactive({
    submit: false
  })

  /** 查询菜单下拉树结构 */
  function getTreeselect() {
    data.menuOptions = []
    listChildren().then((response) => {
      let menuData = response.data
      setI18nTitle(menuData)
      const menu = { menuId: 0, menuName: proxy.$t('category.main'), children: menuData }
      data.menuOptions.push(menu)
    })
  }

  const setI18nTitle = (el) => {
    el.forEach((el) => {
      el.menuName = proxy.$t(el.menuName)
      if (el.children?.length > 0) {
        setI18nTitle(el.children)
      }
    })
  }

  /** 选择图标 */
  function selected(name) {
    form.value.icon = name
    data.showIcon = false
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['menuRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.menuId != undefined) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateMenu(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const add = () => {
    addMenu(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    getTreeselect()
    if (props.id) {
      getMenu(props.id).then((response) => {
        data.form = response.data
      })
    } else {
      // 设置父级菜单ID
      form.value.parentId = props.parentId || 0
    }
  }

  // 表单重置
  function reset() {
    data.showIcon = false
    form.value = {
      menuId: undefined,
      parentId: 0,
      menuName: undefined,
      icon: undefined,
      menuType: 'M',
      orderNum: undefined,
      isFrame: '1',
      isCache: '0',
      visible: '0',
      status: '0'
    }
    proxy.resetForm('menuRef')
  }
</script>

<style scoped>
  .el-icon {
    padding-right: 3px !important;
  }
</style>
