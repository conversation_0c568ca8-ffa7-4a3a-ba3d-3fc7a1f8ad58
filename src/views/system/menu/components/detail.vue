<template>
  <Drawer v-model="open" @open="init" :size="700">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form label-width="120px" style="width: 100%">
          <el-row :gutter="0">
            <!-- 菜单编号 -->
            <el-col :span="12">
              <el-form-item :label="$t('number.menu')">
                <span>{{ form.menuId }}</span>
              </el-form-item>
            </el-col>
            <!-- 菜单名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.menu')">
                <span>{{ $t(form.menuName) }}</span>
              </el-form-item>
            </el-col>
            <!-- 菜单类型 -->
            <el-col :span="12">
              <el-form-item :label="$t('type.menu')">
                <span>{{ getMenuTypeLabel(form.menuType) }}</span>
              </el-form-item>
            </el-col>
            <!-- 菜单图标 -->
            <el-col v-if="form.menuType !== 'F'" :span="12">
              <el-form-item :label="$t('icon.menu')">
                <span v-if="form.icon">
                  <i
                    style="font-size: 22px; margin-right: 8px"
                    :class="`iconfont-sys ${form.icon}`"
                  ></i>

                  {{ form.icon }}
                </span>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            <!-- 显示排序 -->
            <el-col :span="12">
              <el-form-item :label="$t('sort.display')">
                <span>{{ form.orderNum }}</span>
              </el-form-item>
            </el-col>
            <!-- 路由名称 -->
            <el-col v-if="form.menuType === 'C'" :span="12">
              <el-form-item :label="$t('name.route')">
                <span>{{ form.routeName || '-' }}</span>
              </el-form-item>
            </el-col>
            <!-- 是否外链 -->
            <el-col v-if="form.menuType != 'F'" :span="12">
              <el-form-item :label="$t('link.external.question')">
                <span>{{ form.isFrame === '0' ? $t('yes') : $t('no') }}</span>
              </el-form-item>
            </el-col>
            <!-- 路由地址 -->
            <el-col v-if="form.menuType != 'F'" :span="12">
              <el-form-item :label="$t('address.route')">
                <span>{{ form.path || '-' }}</span>
              </el-form-item>
            </el-col>
            <!-- 组件路径 -->
            <el-col v-if="form.menuType === 'C'" :span="12">
              <el-form-item :label="$t('path.component')">
                <span>{{ form.component || '-' }}</span>
              </el-form-item>
            </el-col>
            <!-- 权限标识 -->
            <el-col v-if="form.menuType != 'M'" :span="12">
              <el-form-item :label="$t('character.permission')">
                <span>{{ form.perms || '-' }}</span>
              </el-form-item>
            </el-col>
            <!-- 路由参数 -->
            <el-col v-if="form.menuType === 'C'" :span="12">
              <el-form-item :label="$t('parameters.route')">
                <span>{{ form.query || '-' }}</span>
              </el-form-item>
            </el-col>
            <!-- 是否缓存 -->
            <el-col v-if="form.menuType === 'C'" :span="12">
              <el-form-item :label="$t('cache.question')">
                <span>{{ form.isCache === '0' ? $t('cache') : $t('cache.not') }}</span>
              </el-form-item>
            </el-col>
            <!-- 显示状态 -->
            <el-col v-if="form.menuType != 'F'" :span="12">
              <el-form-item :label="$t('status.display')">
                <dict-tag :options="sys_show_hide" :value="form.visible" />
              </el-form-item>
            </el-col>
            <!-- 菜单状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status.menu')">
                <dict-tag :options="sys_normal_disable" :value="form.status" />
              </el-form-item>
            </el-col>
            <!-- 创建时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.creation')">
                <span>{{ parseTime(form.createTime) }}</span>
              </el-form-item>
            </el-col>
            <!-- 更新时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.update')">
                <span>{{ parseTime(form.updateTime) }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>

      <!-- 上级菜单信息 -->
      <DrawerTitle v-if="form.parentId && form.parentId !== 0">
        <template #default>{{ $t('menu.parent') }}</template>
      </DrawerTitle>
      <DrawerGroup v-if="form.parentId && form.parentId !== 0">
        <el-form label-width="120px" style="width: 100%">
          <el-row :gutter="0">
            <el-col :span="12">
              <el-form-item :label="$t('name.menu')">
                <span>{{ parentMenuName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('type.menu')">
                <span>{{ parentMenuType }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getMenu } from '@/api/system/menu'

  const { proxy } = getCurrentInstance()
  const { sys_show_hide, sys_normal_disable } = proxy.useDict('sys_show_hide', 'sys_normal_disable')

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    parentMenuName: '',
    parentMenuType: ''
  })

  const { form, parentMenuName, parentMenuType } = toRefs(data)

  const getMenuTypeLabel = (type) => {
    switch (type) {
      case 'M':
        return proxy.$t('directory')
      case 'C':
        return proxy.$t('menu')
      case 'F':
        return proxy.$t('button')
      default:
        return '-'
    }
  }

  const getParentMenuInfo = async (parentId) => {
    if (parentId && parentId !== 0) {
      try {
        const response = await getMenu(parentId)
        data.parentMenuName = proxy.$t(response.data.menuName)
        data.parentMenuType = getMenuTypeLabel(response.data.menuType)
      } catch (error) {
        data.parentMenuName = '-'
        data.parentMenuType = '-'
      }
    }
  }

  const init = () => {
    reset()
    if (props.id) {
      getMenu(props.id).then((response) => {
        data.form = response.data
        // 获取父级菜单信息
        getParentMenuInfo(response.data.parentId)
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {}
    data.parentMenuName = ''
    data.parentMenuType = ''
  }
</script>

<style scoped>
  .el-icon {
    padding-right: 3px !important;
  }
</style>
