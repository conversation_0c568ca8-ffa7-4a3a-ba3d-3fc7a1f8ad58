<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:menu:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Sort" plain type="info" @click="toggleExpandAll"
          >{{ $t('expand.collapse') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-if="refreshTable"
      v-loading="loading"
      :data="menuList"
      :total="menuList.length"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      row-key="menuId"
    >
      <el-table-column :label="$t('name.menu')" :show-overflow-tooltip="true" prop="menuName">
        <template #default="{ row }">
          {{ $t(row.menuName) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('icon')" align="center" prop="icon">
        <template #default="scope">
          <i
            style="font-size: 22px"
            :class="`iconfont-sys ${scope.row.icon}`"
            v-if="scope.row.icon.startsWith('iconsys')"
          ></i>
          <svg-icon :icon-class="scope.row.icon" v-else />
        </template>
      </el-table-column>
      <el-table-column :label="$t('sort')" prop="orderNum"></el-table-column>
      <el-table-column
        :label="$t('identifier.permission')"
        :show-overflow-tooltip="true"
        prop="perms"
      ></el-table-column>
      <el-table-column
        :label="$t('path.component')"
        :show-overflow-tooltip="true"
        prop="component"
      ></el-table-column>
      <el-table-column :label="$t('status')" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
        width="230px"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:menu:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
          >
            {{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:menu:add']"
            icon="Plus"
            link
            type="primary"
            @click="handleAdd(scope.row)"
          >
            {{ $t('button.add') }}
          </el-button>
          <el-button
            v-hasPermi="['system:menu:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改菜单对话框 -->
    <add
      v-model:show="open"
      :title="title"
      :id="menuId"
      :parentId="parentId"
      @refreshList="refreshList"
    />
  </div>
</template>

<script name="Menu" setup>
  import { getCurrentInstance } from 'vue'
  import { delMenu, listMenu } from '@/api/system/menu'
  import SvgIcon from '@/components/SvgIcon'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import add from './components/add.vue'

  const { proxy } = getCurrentInstance()

  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const menuList = ref([])
  const open = ref(false)
  const menuId = ref('')
  const parentId = ref(0)
  const loading = ref(true)
  const title = ref('')
  const isExpandAll = ref(false)
  const refreshTable = ref(true)

  const data = reactive({
    fromKey: 'menu',
    fromName: 'menu',
    queryParams: {
      menuName: undefined,
      visible: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询菜单列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    queryParams.value.menuType = 'All'
    listMenu(queryParams.value).then((response) => {
      menuList.value = response.data
      loading.value = false
    })
  }

  /** 新增按钮操作 */
  function handleAdd(row) {
    menuId.value = ''
    parentId.value = row && row.menuId ? row.menuId : 0
    title.value = proxy.$t('menu.add')
    open.value = true
  }

  /** 展开/折叠操作 */
  function toggleExpandAll() {
    refreshTable.value = false
    isExpandAll.value = !isExpandAll.value
    nextTick(() => {
      refreshTable.value = true
    })
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    menuId.value = row.menuId
    title.value = proxy.$t('menu.modify')
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm(proxy.$t('confirm.delete.name') + row.menuName + proxy.$t('item.data.question'))
      .then(function () {
        return delMenu(row.menuId)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  const refreshList = () => {
    getList()
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>
<style scoped>
  .el-icon {
    padding-right: 3px !important;
  }
</style>
