<template>
  <Drawer v-model="open" size="60%" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>
          {{ $t('intl.drawer.basicInfo') }}
        </template>
      </DrawerTitle>

      <DrawerGroup>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('higthQuery.label.title')" prop="title">
              {{ form.title }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('higthQuery.label.businessKey')" prop="busiKey">
              {{ form.busiKey }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('higthQuery.label.description')" prop="description">
              {{ form.description }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
      <DrawerTitle>
        <template #default>
          {{ $t('higthQuery.otherConfig') }}
        </template>
      </DrawerTitle>
      <!-- 添加或修改高级查询列对话框 -->
      <AddDetail
        :id="data.detailId"
        v-model:show="data.detailOpen"
        :busi-key="data.form.busiKey"
        :sortMaxCode="data.sortMaxCode"
        :title="data.detailId ? $t('higthQuery.editColumn') : $t('higthQuery.newColumn')"
        @refresh-list="init"
      />
      <el-tabs v-model="data.tabIdx" class="demo-tabs">
        <el-tab-pane :label="$t('higthQuery.label.columnConfig')" name="column">
          <div class="float-right mb-2">
            <el-button
              v-hasPermi="['system:higthQuery:add']"
              icon="Plus"
              type="primary"
              @click="handleAdd"
              >{{ $t('button.add') }}
            </el-button>
          </div>
          <el-table
            v-loading="loading"
            :data="data.form.list"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column
              :label="$t('higthQuery.columnLabel')"
              :show-overflow-tooltip="true"
              prop="label"
              width="120"
            />
            <el-table-column
              :label="$t('higthQuery.columnName')"
              :show-overflow-tooltip="true"
              prop="column"
              width="120"
            />
            <el-table-column
              :label="$t('higthQuery.columnCondition')"
              :show-overflow-tooltip="true"
              prop="andOr"
            >
              <template #default="scope">
                <dict-tag :options="and_or" :value="scope.row.andOr" />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('higthQuery.columnSort')"
              :show-overflow-tooltip="true"
              prop="sortNum"
            />
            <el-table-column
              :label="$t('higthQuery.columnDisable')"
              :show-overflow-tooltip="true"
              prop="isDisable"
            >
              <template #default="scope">
                <dict-tag :options="sys_yes_no" :value="scope.row.isDisable" />
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('higthQuery.columnAlias')"
              :show-overflow-tooltip="true"
              prop="ruleAlias"
            />
            <el-table-column
              :label="$t('higthQuery.columnDefault')"
              :show-overflow-tooltip="true"
              prop="isDefault"
            >
              <template #default="scope">
                <dict-tag :options="sys_yes_no" :value="scope.row.isDefault" />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('higthQuery.columnType')"
              :show-overflow-tooltip="true"
              prop="type"
            >
              <template #default="scope">
                <dict-tag :options="higth_query_type" :value="scope.row.type" />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('higthQuery.columnCondition')"
              :show-overflow-tooltip="true"
              prop="condition"
            >
              <template #default="scope">
                <dict-tag :options="higth_query_condition" :value="scope.row.condition" />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('higthQuery.componentType')"
              :show-overflow-tooltip="true"
              prop="commonType"
              width="90"
            >
              <template #default="scope">
                <dict-tag :options="hight_query_compoent" :value="scope.row.commonType" />
              </template>
            </el-table-column>

            <el-table-column
              :label="$t('higthQuery.columnAction')"
              class-name="small-padding fixed-width"
              fixed="right"
              width="160"
            >
              <template #default="scope">
                <el-button
                  v-hasPermi="['system:higthQueryDetail:edit']"
                  icon="Edit"
                  link
                  type="primary"
                  @click="handleUpdate(scope.row)"
                  >{{ $t('action.modify') }}
                </el-button>
                <el-button
                  v-hasPermi="['system:higthQueryDetail:remove']"
                  icon="Delete"
                  link
                  type="danger"
                  @click="handleDelete(scope.row)"
                  >{{ $t('button.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </template>
  </Drawer>
</template>

<script setup>
  import { delHigthQueryDetail } from '@/api/system/higthQueryDetail/HigthQueryDetail'
  import { computed, getCurrentInstance, toRefs } from 'vue'
  import { getHigthQuery } from '@/api/system/higthQuery/HigthQuery'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import AddDetail from '@/views/system/higthQueryDetail/components/add.vue'

  const { proxy } = getCurrentInstance()

  const { higth_query_condition, and_or, sys_yes_no, hight_query_compoent, higth_query_type } =
    proxy.useDict(
      'higth_query_condition',
      'and_or',
      'sys_yes_no',
      'hight_query_compoent',
      'higth_query_type'
    )
  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    tabIdx: 'column',
    detailId: '',
    sortMaxCode: 0,
    detailOpen: false
  })

  const { form } = toRefs(data)

  /** 修改按钮操作 */
  function handleUpdate(row) {
    data.detailId = row.id
    data.detailOpen = true
  }

  const handleAdd = () => {
    data.detailId = null
    data.sortMaxCode = Math.max(...data.form.list.map((item) => item.sortNum)) + 1
    data.detailOpen = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id
    proxy.$modal
      .confirm(proxy.$t('higthQuery.message.confirmDeleteCondition', { nameArr: row.label }))
      .then(function () {
        return delHigthQueryDetail(idKeys)
      })
      .then(() => {
        init()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  const init = () => {
    if (props.id) {
      getHigthQuery(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
