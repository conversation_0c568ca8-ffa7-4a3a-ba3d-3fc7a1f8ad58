<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default> {{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="higthQueryRef" :model="form" :rules="rules">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('higthQuery.label.title')" prop="title">
                <el-input v-model="form.title" :placeholder="$t('higthQuery.placeholder.title')" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('higthQuery.busiKey')" prop="busiKey">
                <el-input
                  v-model="form.busiKey"
                  :placeholder="$t('higthQuery.placeholder.businessKey')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('higthQuery.label.description')" prop="description">
                <el-input
                  v-model="form.description"
                  :placeholder="$t('higth.input.content')"
                  style="width: 100%"
                  type="textarea"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, getCurrentInstance, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import {
    addHigthQuery,
    getHigthQuery,
    updateHigthQuery
  } from '@/api/system/higthQuery/HigthQuery'

  const { proxy } = getCurrentInstance()

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      title: [
        { required: true, message: proxy.$t('higthQuery.message.titleRequired'), trigger: 'blur' }
      ],
      busiKey: [
        {
          required: true,
          message: proxy.$t('higthQuery.message.businessKeyRequired'),
          trigger: 'blur'
        }
      ],
      description: [
        {
          required: true,
          message: proxy.$t('higthQuery.message.descriptionRequired'),
          trigger: 'blur'
        }
      ]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['higthQueryRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateHigthQuery(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addHigthQuery(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))

        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getHigthQuery(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      title: null,
      busiKey: null,
      description: null
    }
    proxy.resetForm('higthQueryRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
