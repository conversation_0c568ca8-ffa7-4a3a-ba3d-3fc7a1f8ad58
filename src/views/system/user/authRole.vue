<template>
  <div class="page-content">
    <h4 class="form-header h4"> {{ $t('intl.drawer.basicInfo') }} </h4>
    <el-form :model="form">
      <el-row>
        <el-col :offset="2" :span="8">
          <el-form-item :label="$t('nickname.user')" prop="nickName">
            <el-input v-model="form.nickName" disabled />
          </el-form-item>
        </el-col>
        <el-col :offset="2" :span="8">
          <el-form-item :label="$t('account.login')" prop="userName">
            <el-input v-model="form.userName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <h4 class="form-header h4">{{ $t('information.role') }}</h4>

    <art-table
      v-loading="loading"
      :data="roles.slice((pageNum - 1) * pageSize, pageNum * pageSize)"
      ref="roleRef"
      v-model:page-num="pageNum"
      v-model:page-size="pageSize"
      :total="total"
      rowKey="roleKey"
      @row-click="clickRow"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('number.serial')" align="center" type="index" width="55">
        <template #default="scope">
          <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column :reserve-selection="true" type="selection" width="55"></el-table-column>
      <el-table-column :label="$t('number.role')" align="center" prop="roleId" />
      <el-table-column :label="$t('name.role')" align="center" prop="roleName" />
      <el-table-column :label="$t('character.permission')" align="center" prop="roleKey" />
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </art-table>

    <el-form label-width="100px">
      <div style="text-align: center; margin-left: -120px; margin-top: 15px">
        <el-button type="primary" @click="submitForm()">{{ $t('intl.btn.submit') }}</el-button>
        <el-button @click="close()">{{ $t('action.return') }}</el-button>
      </div>
    </el-form>
  </div>
</template>

<script name="AuthRole" setup>
  import { getCurrentInstance } from 'vue'
  import { getAuthRole, updateAuthRole } from '@/api/system/user'

  const { proxy } = getCurrentInstance()

  const route = useRoute()

  const loading = ref(true)
  const total = ref(0)
  const pageNum = ref(1)
  const pageSize = ref(10)
  const roleIds = ref([])
  const roles = ref([])
  const form = ref({
    nickName: undefined,
    userName: undefined,
    userId: undefined
  })

  /** 单击选中行数据 */
  function clickRow(row) {
    proxy.$refs['roleRef'].toggleRowSelection(row)
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    console.log(selection)
    roleIds.value = selection.map((item) => item.roleId)
  }
  /** 关闭按钮 */
  function close() {
    proxy.$tab.closeOpenPage()
  }

  /** 提交按钮 */
  function submitForm() {
    const userId = form.value.userId
    const rIds = roleIds.value.join(',')
    updateAuthRole({ userId: userId, roleIds: rIds }).then((response) => {
      proxy.$modal.msgSuccess(proxy.$t('authorization.success'))
      close()
    })
  }

  ;(() => {
    const userId = route.params && route.params.userId
    if (userId) {
      loading.value = true
      getAuthRole(userId).then((response) => {
        form.value = response.user
        roles.value = response.roles
        total.value = roles.value.length
        nextTick(() => {
          roles.value.forEach((row) => {
            if (row.flag) {
              proxy.$refs['roleRef'].toggleRowSelection(row)
            }
          })
        })
        loading.value = false
      })
    }
  })()
</script>
