<template>
  <Drawer v-model="open" @open="init" :size="800">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form label-width="120px" style="width: 100%;">
          <el-row :gutter="0">
            <!-- 公告编号 -->
            <el-col :span="12">
              <el-form-item :label="$t('number.notice')">
                <span>{{ form.noticeId }}</span>
              </el-form-item>
            </el-col>
            <!-- 公告标题 -->
            <el-col :span="12">
              <el-form-item :label="$t('title.notice')">
                <span>{{ form.noticeTitle }}</span>
              </el-form-item>
            </el-col>
            <!-- 公告类型 -->
            <el-col :span="12">
              <el-form-item :label="$t('type.notice')">
                <dict-tag :options="sys_notice_type" :value="form.noticeType" />
              </el-form-item>
            </el-col>
            <!-- 状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status')">
                <dict-tag :options="sys_notice_status" :value="form.status" />
              </el-form-item>
            </el-col>
            <!-- 创建者 -->
            <el-col :span="12">
              <el-form-item :label="$t('creator')">
                <span>{{ form.createBy || '-' }}</span>
              </el-form-item>
            </el-col>
            <!-- 创建时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.creation')">
                <span>{{ parseTime(form.createTime) }}</span>
              </el-form-item>
            </el-col>
            <!-- 更新时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.update')">
                <span>{{ parseTime(form.updateTime) }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>

      <!-- 公告内容 -->
      <DrawerTitle>
        <template #default>{{ $t('content') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <div class="notice-content" v-html="form.noticeContent"></div>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getNotice } from '@/api/system/notice'

  const { proxy } = getCurrentInstance()
  const { sys_notice_status, sys_notice_type } = proxy.useDict(
    'sys_notice_status',
    'sys_notice_type'
  )

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    reset()
    if (props.id) {
      getNotice(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {}
  }
</script>

<style scoped>
.notice-content {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
  min-height: 200px;
  line-height: 1.6;
}

.notice-content :deep(img) {
  max-width: 100%;
  height: auto;
}

.notice-content :deep(p) {
  margin: 8px 0;
}

.notice-content :deep(h1),
.notice-content :deep(h2),
.notice-content :deep(h3),
.notice-content :deep(h4),
.notice-content :deep(h5),
.notice-content :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: bold;
}
</style>
