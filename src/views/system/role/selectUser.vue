<template>
  <!-- 授权用户 -->
  <el-dialog v-model="visible" :title="$t('user.select')" append-to-body top="5vh" width="800px">
    <el-form ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item :label="$t('name.user')" prop="userName">
        <el-input
          v-model="queryParams.userName"
          :placeholder="$t('name.user.input')"
          clearable
          style="width: 180px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('number.mobile')" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          :placeholder="$t('number.mobile.input')"
          clearable
          style="width: 180px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">{{
          $t('action.search')
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table
        ref="refTable"
        :data="userList"
        height="540px"
        @row-click="clickRow"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column :label="$t('name.user')" :show-overflow-tooltip="true" prop="userName" />
        <el-table-column
          :label="$t('nickname.user')"
          :show-overflow-tooltip="true"
          prop="nickName"
        />
        <el-table-column :label="$t('email')" :show-overflow-tooltip="true" prop="email" />
        <el-table-column :label="$t('mobile')" :show-overflow-tooltip="true" prop="phonenumber" />
        <el-table-column :label="$t('status')" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNum"
        :total="total"
        @pagination="getList"
      />
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSelectUser">{{ $t('action.confirm') }}</el-button>
        <el-button @click="visible = false">{{ $t('common.cancel') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script name="SelectUser" setup>
  import { getCurrentInstance } from 'vue'
  import { authUserSelectAll, unallocatedUserList } from '@/api/system/role'

  const { proxy } = getCurrentInstance()

  const props = defineProps({
    roleId: {
      type: [Number, String]
    }
  })

  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const userList = ref([])
  const visible = ref(false)
  const total = ref(0)
  const userIds = ref([])

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
    roleId: undefined,
    userName: undefined,
    phonenumber: undefined
  })

  // 显示弹框
  function show() {
    queryParams.roleId = props.roleId
    getList()
    visible.value = true
  }

  /**选择行 */
  function clickRow(row) {
    proxy.$refs['refTable'].toggleRowSelection(row)
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    userIds.value = selection.map((item) => item.userId)
  }

  // 查询表数据
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    unallocatedUserList(queryParams).then((res) => {
      userList.value = res.rows
      total.value = res.total
    })
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.pageNum = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    handleQuery()
  }

  const emit = defineEmits(['ok'])

  /** 选择授权用户操作 */
  function handleSelectUser() {
    const roleId = queryParams.roleId
    const uIds = userIds.value.join(',')
    if (uIds == '') {
      proxy.$modal.msgError(proxy.$t('user.assign.select'))
      return
    }
    authUserSelectAll({ roleId: roleId, userIds: uIds }).then((res) => {
      proxy.$modal.msgSuccess(res.msg)
      visible.value = false
      emit('ok')
    })
  }

  defineExpose({
    show
  })
</script>
