<template>
  <Drawer v-model="open" @open="init" :size="600">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm">
          {{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form :model="form" label-width="120px">
          <el-row>
            <!-- 角色名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.role')">
                <el-input v-model="form.roleName" :disabled="true" />
              </el-form-item>
            </el-col>
            <!-- 权限字符 -->
            <el-col :span="12">
              <el-form-item :label="$t('character.permission')">
                <el-input v-model="form.roleKey" :disabled="true" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- 权限范围 -->
            <el-col :span="24">
              <el-form-item :label="$t('scope.permission')">
                <el-select
                  v-model="form.dataScope"
                  @change="dataScopeSelectChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dataScopeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>

      <!-- 数据权限 -->
      <DrawerTitle v-show="form.dataScope === '2'">
        <template #default>{{ $t('permission.data') }}</template>
      </DrawerTitle>
      <DrawerGroup v-show="form.dataScope === '2'">
        <div class="tree-controls">
          <el-checkbox v-model="deptExpand" @change="handleCheckedTreeExpand($event, 'dept')">
            {{ $t('expand.collapse') }}
          </el-checkbox>
          <el-checkbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event, 'dept')">
            {{ $t('select.all.none') }}
          </el-checkbox>
          <el-checkbox
            v-model="form.deptCheckStrictly"
            @change="handleCheckedTreeConnect($event, 'dept')"
          >
            {{ $t('linkage.parent.child') }}
          </el-checkbox>
        </div>
        <el-tree
          ref="deptRef"
          :check-strictly="!form.deptCheckStrictly"
          :data="deptOptions"
          :empty-text="$t('loading')"
          :props="{ label: 'label', children: 'children' }"
          class="tree-border"
          default-expand-all
          node-key="id"
          show-checkbox
        ></el-tree>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs, nextTick } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { dataScope, deptTreeSelect, getRole } from '@/api/system/role'

  const { proxy } = getCurrentInstance()

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    deptOptions: [],
    deptExpand: true,
    deptNodeAll: false,
    originalCheckedKeys: [], // 保存原始完全选中的部门节点（叶子节点）
    originalHalfCheckedKeys: [] // 保存原始半选中的部门节点（父节点）
  })

  const { form, deptOptions, deptExpand, deptNodeAll } = toRefs(data)
  const deptRef = ref(null)

  const loadingBtn = reactive({
    submit: false
  })

  /** 数据范围选项*/
  const dataScopeOptions = [
    { value: '1', label: proxy.$t('permissions.data.all') },
    { value: '2', label: proxy.$t('data.permission.custom') },
    { value: '3', label: proxy.$t('permission.data.department') },
    { value: '4', label: proxy.$t('permission.data.department.below') },
    { value: '5', label: proxy.$t('permission.data.only') }
  ]

  /** 根据角色ID查询部门树结构 */
  function getDeptTree(roleId) {
    return deptTreeSelect(roleId).then((response) => {
      data.deptOptions = response.depts
      return response
    })
  }

  /** 树权限（展开/折叠）*/
  function handleCheckedTreeExpand(value, type) {
    if (type === 'dept') {
      let treeList = data.deptOptions
      for (let i = 0; i < treeList.length; i++) {
        deptRef.value.store.nodesMap[treeList[i].id].expanded = value
      }
    }
  }

  /** 树权限（全选/全不选） */
  function handleCheckedTreeNodeAll(value, type) {
    if (type === 'dept') {
      deptRef.value.setCheckedNodes(value ? data.deptOptions : [])
    }
  }

  /** 树权限（父子联动） */
  function handleCheckedTreeConnect(value, type) {
    if (type === 'dept') {
      form.value.deptCheckStrictly = value ? true : false
    }
  }

  /** 所有部门节点数据 */
  function getDeptAllCheckedKeys() {
    // 目前被选中的部门节点
    let checkedKeys = deptRef.value.getCheckedKeys()
    // 半选中的部门节点
    let halfCheckedKeys = deptRef.value.getHalfCheckedKeys()
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
    return checkedKeys
  }

  /** 选择角色权限范围触发 */
  function dataScopeSelectChange(value) {
    if (!deptRef.value) return

    if (value === '2') {
      // 切换到自定义权限时，恢复之前保存的选中状态
      nextTick(() => {
        if (data.originalCheckedKeys.length > 0 || data.originalHalfCheckedKeys.length > 0) {
          // 先设置完全选中的节点（叶子节点）
          deptRef.value.setCheckedKeys(data.originalCheckedKeys)

          // 然后手动设置半选中的节点（父节点）
          nextTick(() => {
            data.originalHalfCheckedKeys.forEach((key) => {
              const node = deptRef.value.getNode(key)
              if (node) {
                node.indeterminate = true
              }
            })
          })
        }
      })
    } else {
      // 切换到其他权限时，保存当前选中状态并清空显示
      const currentChecked = deptRef.value.getCheckedKeys()
      const currentHalfChecked = deptRef.value.getHalfCheckedKeys()

      // 保存当前状态
      if (currentChecked.length > 0 || currentHalfChecked.length > 0) {
        data.originalCheckedKeys = [...currentChecked]
        data.originalHalfCheckedKeys = [...currentHalfChecked]
      }

      // 清空当前显示的选中状态
      deptRef.value.setCheckedKeys([])
    }
  }

  /** 提交按钮 */
  function submitForm() {
    if (form.value.roleId !== undefined) {
      loadingBtn.submit = true
      // 如果是自定义权限，获取当前选中的部门节点
      if (form.value.dataScope === '2') {
        form.value.deptIds = getDeptAllCheckedKeys()
        // 更新保存的状态为当前实际状态
        if (deptRef.value) {
          data.originalCheckedKeys = deptRef.value.getCheckedKeys()
          data.originalHalfCheckedKeys = deptRef.value.getHalfCheckedKeys()
        }
      } else {
        form.value.deptIds = []
      }

      dataScope(form.value)
        .then(() => {
          proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
          emits('refreshList')
          open.value = false
        })
        .finally(() => {
          loadingBtn.submit = false
        })
    }
  }

  const init = () => {
    reset()
    if (props.id) {
      const deptTreeSelect = getDeptTree(props.id)
      getRole(props.id).then((response) => {
        data.form = response.data
        nextTick(() => {
          deptTreeSelect.then((res) => {
            nextTick(() => {
              if (deptRef.value) {
                // 先设置选中状态
                deptRef.value.setCheckedKeys(res.checkedKeys)

                // 等待树渲染完成后，获取实际的选中和半选中状态
                nextTick(() => {
                  data.originalCheckedKeys = deptRef.value.getCheckedKeys()
                  data.originalHalfCheckedKeys = deptRef.value.getHalfCheckedKeys()
                })
              }
            })
          })
        })
      })
    }
  }

  // 表单重置
  function reset() {
    data.deptExpand = true
    data.deptNodeAll = false
    data.originalCheckedKeys = []
    data.originalHalfCheckedKeys = []
    form.value = {
      roleId: undefined,
      roleName: undefined,
      roleKey: undefined,
      dataScope: undefined,
      deptIds: [],
      deptCheckStrictly: true
    }
  }
</script>

<style scoped>
  .tree-controls {
    margin-bottom: 10px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }

  .tree-border {
    margin-top: 10px;
    border: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color);
    border-radius: 4px;
    width: 100%;
    padding: 10px;
    min-height: 200px;
  }

  :deep(.el-tree-node__content) {
    height: 32px;
    line-height: 32px;
  }

  :deep(.el-tree-node__content:hover) {
    background-color: var(--el-color-primary-light-9);
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }

  :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }
</style>
