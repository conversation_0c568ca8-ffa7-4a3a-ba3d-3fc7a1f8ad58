<template>
  <div class="page-content user container">
    <div class="content">
      <div class="left-wrap">
        <div class="user-wrap box-style">
          <userAvatar />
          <h2 class="name">{{ userInfo.username }}</h2>
          <p class="des">{{ $t('template.management.system') }}</p>

          <div class="outer-info">
            <div>
              <i class="iconfont-sys">&#xe7fd;</i>
              <span>{{ userInfo.nickName }}</span>
            </div>
            <div class="flex">
              <svg-icon icon-class="phone" />
              <span>{{ userInfo.phonenumber }}</span>
            </div>
            <div>
              <i class="iconfont-sys">&#xe806;</i>
              <span>{{ userInfo.email }}</span>
            </div>
            <div class="flex">
              <svg-icon icon-class="tree" />
              <span v-if="userInfo.dept">{{ userInfo.dept.deptName }} / {{ state.postGroup }}</span>
            </div>
            <div class="flex">
              <svg-icon icon-class="peoples" />
              <span v-if="userInfo.dept">{{ state.roleGroup }} </span>
            </div>
            <div class="flex">
              <svg-icon icon-class="date" />
              <span v-if="userInfo.dept">{{ userInfo.createTime }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="right-wrap">
        <div class="info box-style">
          <h1 class="title">{{ $t('settings.basic') }}</h1>
          <el-tabs v-model="activeTab" class="big-ml-10 big-mr-10">
            <el-tab-pane :label="$t('information.basic')" name="userInfo">
              <UserInfo :user="state.user" />
            </el-tab-pane>
            <el-tab-pane :label="$t('password.modify')" name="resetPwd">
              <resetPwd />
            </el-tab-pane>
            <el-tab-pane :label="$t('application.third.party')" name="otherApp">
              <otherApp :auths="state.auths" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getCurrentInstance } from 'vue'
  import { useUserStore } from '@/store/modules/user'
  import UserInfo from './userInfo.vue'
  import resetPwd from './resetPwd'
  import otherApp from './otherApp'
  import userAvatar from './userAvatar'
  import { getUserProfile } from '@/api/system/user'
  import { useRoute } from 'vue-router'

  const { proxy } = getCurrentInstance()

  const userStore = useUserStore()
  const userInfo = computed(() => userStore.getUserInfo)

  const route = useRoute()
  const activeTab = ref('userInfo')
  //绑定第三方应用登录
  const { params, query } = route
  if (query['activeTab']) {
    activeTab.value = query['activeTab']
  }
  const state = reactive({
    user: {},
    auths: [],
    roleGroup: {},
    postGroup: {}
  })

  function getUser() {
    getUserProfile().then((response) => {
      state.user = response.data
      state.roleGroup = response.roleGroup
      state.postGroup = response.postGroup
      state.auths = response.auths
    })
  }

  getUser()
</script>

<style lang="scss">
  .user {
    .icon {
      width: 1.4em;
      height: 1.4em;
      overflow: hidden;
      vertical-align: -0.15em;
      fill: currentcolor;
    }
  }
</style>

<style lang="scss" scoped>
  .page-content {
    width: 100%;
    height: 100%;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;

    $box-radius: calc(var(--custom-radius) + 4px);

    .box-style {
      border: 1px solid var(--art-border-color);
    }

    .content {
      position: relative;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;

      .left-wrap {
        width: 450px;
        margin-right: 25px;

        .user-wrap {
          position: relative;
          height: 600px;
          padding: 35px 40px;
          overflow: hidden;
          text-align: center;
          background: var(--art-main-bg-color);
          border-radius: $box-radius;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 200px;
            object-fit: cover;
          }

          .avatar {
            position: relative;
            z-index: 10;
            width: 80px;
            height: 80px;
            margin-top: 120px;
            object-fit: cover;
            border: 2px solid #fff;
            border-radius: 50%;
          }

          .name {
            margin-top: 20px;
            font-size: 22px;
            font-weight: 400;
          }

          .des {
            margin-top: 20px;
            font-size: 14px;
          }

          .outer-info {
            width: 300px;
            margin: auto;
            margin-top: 30px;
            text-align: left;

            > div {
              margin-top: 10px;

              span {
                margin-left: 8px;
                font-size: 14px;
              }
            }
          }

          .lables {
            margin-top: 40px;

            h3 {
              font-size: 15px;
              font-weight: 500;
            }

            > div {
              display: flex;
              flex-wrap: wrap;
              justify-content: center;
              margin-top: 15px;

              > div {
                padding: 3px 6px;
                margin: 0 10px 10px 0;
                font-size: 12px;
                background: var(--art-main-bg-color);
                border: 1px solid var(--art-border-color);
                border-radius: 2px;
              }
            }
          }
        }

        .gallery {
          margin-top: 25px;
          border-radius: 10px;

          .item {
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }

      .right-wrap {
        flex: 1;
        overflow: hidden;
        border-radius: $box-radius;

        .info {
          background: var(--art-main-bg-color);
          border-radius: $box-radius;

          .title {
            padding: 15px 25px;
            font-size: 20px;
            font-weight: 400;
            color: var(--art-text-gray-800);
            border-bottom: 1px solid var(--art-border-color);
          }

          .form {
            box-sizing: border-box;
            padding: 30px 25px;

            > .el-row {
              .el-form-item {
                width: calc(50% - 10px);
              }

              .el-input,
              .el-select {
                width: 100%;
              }
            }

            .right-input {
              margin-left: 20px;
            }

            .el-form-item-right {
              display: flex;
              align-items: center;
              justify-content: end;

              .el-button {
                width: 110px !important;
              }
            }
          }
        }
      }
    }
  }

  @media only screen and (max-width: $device-ipad-vertical) {
    .page-content {
      .content {
        display: block;
        margin-top: 5px;

        .left-wrap {
          width: 100%;
        }

        .right-wrap {
          width: 100%;
          margin-top: 15px;
        }
      }
    }
  }
</style>
