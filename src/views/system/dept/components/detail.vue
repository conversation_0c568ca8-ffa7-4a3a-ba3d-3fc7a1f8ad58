<template>
  <Drawer v-model="open" @open="init" :size="700">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form label-width="120px" style="width: 100%">
          <el-row :gutter="0">
            <!-- 部门名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.department')">
                <span>{{ form.deptName }}</span>
              </el-form-item>
            </el-col>
            <!-- 显示排序 -->
            <el-col :span="12">
              <el-form-item :label="$t('sort.display')">
                <span>{{ form.orderNum }}</span>
              </el-form-item>
            </el-col>
            <!-- 负责人 -->
            <el-col :span="12">
              <el-form-item :label="$t('person.charge')">
                <span>{{ form.leader || '-' }}</span>
              </el-form-item>
            </el-col>
            <!-- 联系电话 -->
            <el-col :span="12">
              <el-form-item :label="$t('number.contact')">
                <span>{{ form.phone || '-' }}</span>
              </el-form-item>
            </el-col>
            <!-- 邮箱 -->
            <el-col :span="12">
              <el-form-item :label="$t('email')">
                <span>{{ form.email || '-' }}</span>
              </el-form-item>
            </el-col>
            <!-- 部门状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status.department')">
                <dict-tag :options="sys_normal_disable" :value="form.status" />
              </el-form-item>
            </el-col>
            <!-- 创建时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.creation')">
                <span>{{ parseTime(form.createTime) }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>

      <!-- 上级部门信息 -->
      <DrawerTitle v-if="form.parentId && form.parentId !== 0">
        <template #default>{{ $t('department.parent') }}</template>
      </DrawerTitle>
      <DrawerGroup v-if="form.parentId && form.parentId !== 0">
        <el-form label-width="120px" style="width: 100%">
          <el-row :gutter="0">
            <el-col :span="12">
              <el-form-item :label="$t('name.department')">
                <span>{{ parentDeptName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('person.charge')">
                <span>{{ parentDeptLeader }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getDept } from '@/api/system/dept'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    parentDeptName: '',
    parentDeptLeader: ''
  })

  const { form, parentDeptName, parentDeptLeader } = toRefs(data)

  const getParentDeptInfo = async (parentId) => {
    if (parentId && parentId !== 0) {
      try {
        const response = await getDept(parentId)
        data.parentDeptName = response.data.deptName
        data.parentDeptLeader = response.data.leader || '-'
      } catch (error) {
        data.parentDeptName = '-'
        data.parentDeptLeader = '-'
      }
    }
  }

  const init = () => {
    reset()
    if (props.id) {
      getDept(props.id).then((response) => {
        data.form = response.data
        // 获取父级部门信息
        getParentDeptInfo(response.data.parentId)
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {}
    data.parentDeptName = ''
    data.parentDeptLeader = ''
  }
</script>

<style scoped>
  /* 组件样式 */
</style>
