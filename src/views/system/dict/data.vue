<template>
  <div class="page-content">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item :label="$t('name.dictionary')" prop="dictType">
        <el-select v-model="queryParams.dictType" style="width: 200px">
          <el-option
            v-for="item in typeOptions"
            :key="item.dictId"
            :label="item.dictName"
            :value="item.dictType"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('label.dictionary')" prop="dictLabel">
        <el-input
          v-model="queryParams.dictLabel"
          :placeholder="$t('label.dictionary.input')"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('status')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="$t('status.data')"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="$t(dict.type + '.' + dict.value)"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">{{
          $t('action.search')
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('action.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Close" plain type="warning" @click="handleClose"
          >{{ $t('action.close') }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!--<el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" height="540px">-->
    <art-table
      v-loading="loading"
      :data="dataList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      row-key="dictCode"
      @search="getList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('code.dictionary')" align="center" prop="dictCode" />
      <el-table-column :label="$t('label.dictionary')" align="center" prop="dictLabel">
        <template #default="scope">
          <span
            v-if="
              (scope.row.listClass == '' || scope.row.listClass == 'default') &&
              (scope.row.cssClass == '' || scope.row.cssClass == null)
            "
            >{{ scope.row.dictLabel }}</span
          >
          <el-tag
            v-else
            :class="scope.row.cssClass"
            :type="scope.row.listClass == 'primary' ? '' : scope.row.listClass"
            >{{ scope.row.dictLabel }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('value.key.dictionary')" align="center" prop="dictValue" />
      <el-table-column :label="$t('sort.dictionary')" align="center" prop="dictSort" />
      <el-table-column :label="$t('status')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('remarks')"
        :show-overflow-tooltip="true"
        align="center"
        prop="remark"
      />
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
        width="160"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:dict:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
          >
            {{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:dict:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="500px">
      <el-form ref="dataRef" :model="form" :rules="rules">
        <el-form-item :label="$t('type.dictionary')">
          <el-input v-model="form.dictType" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('label.data')" prop="dictLabel">
          <el-input v-model="form.dictLabel" :placeholder="$t('label.data.input')" />
        </el-form-item>
        <el-form-item :label="$t('value.key.data')" prop="dictValue">
          <el-input v-model="form.dictValue" :placeholder="$t('key.value.data.input')" />
        </el-form-item>
        <el-form-item :label="$t('attribute.style')" prop="cssClass">
          <el-input v-model="form.cssClass" :placeholder="$t('attribute.style.input')" />
        </el-form-item>
        <el-form-item :label="$t('sort.display')" prop="dictSort">
          <el-input-number v-model="form.dictSort" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item :label="$t('style.echo')" prop="listClass">
          <el-select v-model="form.listClass">
            <el-option
              v-for="item in listClassOptions"
              :key="item.value"
              :label="item.label + '(' + item.value + ')'"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value"
              >{{ $t(dict.type + '.' + dict.value) }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('remarks')" prop="remark">
          <el-input
            v-model="form.remark"
            :placeholder="$t('content.input')"
            type="textarea"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('action.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="Data" setup>
  import { getCurrentInstance } from 'vue'
  import useDictStore from '@/store/modules/dict'
  import { getType, optionselect as getDictOptionselect } from '@/api/system/dict/type'
  import { addData, delData, getData, listData, updateData } from '@/api/system/dict/data'

  const { proxy } = getCurrentInstance()

  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const dataList = ref([])
  const open = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const defaultDictType = ref('')
  const typeOptions = ref([])
  const route = useRoute()
  // 数据标签回显样式
  const listClassOptions = ref([
    { value: 'default', label: proxy.$t('default') },
    { value: 'primary', label: proxy.$t('major') },
    { value: 'success', label: proxy.$t('status.success') },
    { value: 'info', label: proxy.$t('information') },
    { value: 'warning', label: proxy.$t('warning') },
    { value: 'danger', label: proxy.$t('danger') }
  ])

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      dictType: undefined,
      dictLabel: undefined,
      status: undefined
    },
    rules: {
      dictLabel: [{ required: true, message: proxy.$t('label.data.empty'), trigger: 'blur' }],
      dictValue: [{ required: true, message: proxy.$t('value.key.data.empty'), trigger: 'blur' }],
      dictSort: [{ required: true, message: proxy.$t('order.data.empty'), trigger: 'blur' }]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  /** 查询字典类型详细 */
  function getTypes(dictId) {
    getType(dictId).then((response) => {
      queryParams.value.dictType = response.data.dictType
      defaultDictType.value = response.data.dictType
      getList()
    })
  }

  /** 查询字典类型列表 */
  function getTypeList() {
    getDictOptionselect().then((response) => {
      typeOptions.value = response.data
    })
  }

  /** 查询字典数据列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listData(queryParams.value).then((response) => {
      dataList.value = response.rows
      total.value = response.total
      loading.value = false
    })
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false
    reset()
  }

  /** 表单重置 */
  function reset() {
    form.value = {
      dictCode: undefined,
      dictLabel: undefined,
      dictValue: undefined,
      cssClass: undefined,
      listClass: 'default',
      dictSort: 0,
      status: '0',
      remark: undefined
    }
    proxy.resetForm('dataRef')
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }

  /** 返回按钮操作 */
  function handleClose() {
    const obj = { path: '/system/dict' }
    proxy.$tab.closeOpenPage(obj)
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef')
    queryParams.value.dictType = defaultDictType.value
    handleQuery()
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset()
    open.value = true
    title.value = proxy.$t('data.dictionary.add')
    form.value.dictType = queryParams.value.dictType
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.dictCode)
    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset()
    const dictCode = row.dictCode || ids.value
    getData(dictCode).then((response) => {
      form.value = response.data
      open.value = true
      title.value = proxy.$t('data.dictionary.modify')
    })
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['dataRef'].validate((valid) => {
      if (valid) {
        if (form.value.dictCode != undefined) {
          updateData(form.value).then((response) => {
            useDictStore().removeDict(queryParams.value.dictType)
            proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
            open.value = false
            getList()
          })
        } else {
          addData(form.value).then((response) => {
            useDictStore().removeDict(queryParams.value.dictType)
            proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
            open.value = false
            getList()
          })
        }
      }
    })
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const dictCodes = row.dictCode || ids.value
    proxy.$modal
      .confirm(
        proxy.$t('confirm.delete.dictionary.code') + dictCodes + proxy.$t('item.data.question')
      )
      .then(function () {
        return delData(dictCodes)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
        useDictStore().removeDict(queryParams.value.dictType)
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/dict/data/export',
      {
        ...queryParams.value
      },
      `dict_data_${new Date().getTime()}.xlsx`
    )
  }

  getTypes(route.params && route.params.dictId)
  getTypeList()
</script>
