<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:remove']"
          icon="Refresh"
          plain
          type="danger"
          @click="handleRefreshCache"
          >{{ $t('cache.refresh') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dict:remove']"
          icon="Refresh"
          plain
          type="warning"
          @click="handleRefreshI18n"
          >{{ $t('dict.i18n.reload') }}
        </el-button>
      </el-col>
      <right-toolbar @queryTable="getList"></right-toolbar>
    </el-row>

    <art-table
      v-loading="loading"
      :data="typeList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="getList"
      row-key="dictId"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('number.dictionary')" align="center" prop="dictId" />
      <el-table-column
        :label="$t('name.dictionary')"
        :show-overflow-tooltip="true"
        align="center"
        prop="dictName"
      >
        <template #default="{ row }">
          <el-link type="primary" @click="handleDetail(row)">{{ row.dictName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('type.dictionary')" :show-overflow-tooltip="true" align="center">
        <template #default="scope">
          <router-link :to="'/system/dict-data/index/' + scope.row.dictId" class="link-type">
            <span>{{ scope.row.dictType }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('status')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('remarks')"
        :show-overflow-tooltip="true"
        align="center"
        prop="remark"
      />
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
        width="160"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:dict:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
          >
            {{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:dict:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改字典对话框 -->
    <add v-model:show="open" :title="title" :id="dictId" @refreshList="refreshList" />

    <!-- 字典详情对话框 -->
    <detail v-model:show="detailShow" :title="title" :id="dictId" />
  </div>
</template>

<script name="Dict" setup>
  import { getCurrentInstance } from 'vue'
  import { reloadI18n } from '@/api/system/dict/data'
  import useDictStore from '@/store/modules/dict'
  import { delType, listType, refreshCache } from '@/api/system/dict/type'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import add from './components/add.vue'
  import detail from './components/detail.vue'

  const { proxy } = getCurrentInstance()

  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const typeList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const dictId = ref('')
  const loading = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')
  const dateRange = ref([])

  const data = reactive({
    fromKey: 'dictType',
    fromName: 'dictType',
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      dictName: undefined,
      dictType: undefined,
      status: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询字典类型列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listType(proxy.addDateRange(queryParams.value, dateRange.value)).then(({ data }) => {
      typeList.value = data.records
      total.value = data.totalRow
      loading.value = false
    })
  }

  /** 新增按钮操作 */
  function handleAdd() {
    dictId.value = ''
    title.value = proxy.$t('type.dictionary.add')
    open.value = true
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.dictId)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    dictId.value = row.dictId || ids.value
    title.value = proxy.$t('type.dictionary.modify')
    open.value = true
  }

  /** 查看详情操作 */
  function handleDetail(row) {
    dictId.value = row.dictId
    title.value = proxy.$t('type.dictionary.detail')
    detailShow.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const dictIds = row.dictId || ids.value
    proxy.$modal
      .confirm(
        proxy.$t('confirm.delete.dictionary.number') + dictIds + proxy.$t('item.data.question')
      )
      .then(function () {
        return delType(dictIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/dict/type/export',
      {
        ...queryParams.value
      },
      `dict_${new Date().getTime()}.xlsx`
    )
  }

  /** 刷新缓存按钮操作 */
  function handleRefreshCache() {
    refreshCache().then(() => {
      proxy.$modal.msgSuccess(proxy.$t('refresh.success'))
      useDictStore().cleanDict()
    })
  }

  const handleRefreshI18n = () => {
    reloadI18n().then(() => {
      proxy.$modal.msgSuccess(proxy.$t('refresh.success'))
      location.reload()
    })
  }

  const refreshList = () => {
    getList()
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>
