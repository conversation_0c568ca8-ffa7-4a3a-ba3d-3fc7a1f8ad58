export default [
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'and_or',
    default: true,
    label: '条件',
    type: 'select',
    condition: '=',
    dictKey: 'and_or',
    commonType: 'dict'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'sort_num',
    default: true,
    label: '排序',
    type: 'num',
    condition: '=',
    commonType: 'input'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'is_disable',
    default: true,
    label: '是否禁用',
    type: 'select',
    condition: '=',
    dictKey: 'sys_yes_no',
    commonType: 'dict'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'rule_alias',
    default: true,
    label: '表别名',
    type: 'string',
    condition: 'like',
    commonType: 'input'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'column',
    default: true,
    label: '列名',
    type: 'string',
    condition: 'like',
    commonType: 'input'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'is_default',
    default: true,
    label: '是否默认',
    type: 'string',
    condition: 'like',
    commonType: 'input'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'label',
    default: true,
    label: '标签名',
    type: 'string',
    condition: 'like',
    commonType: 'input'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'type',
    default: true,
    label: '类型',
    type: 'select',
    condition: '=',
    dictKey: 'higth_query_type',
    commonType: 'dict'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'condition',
    default: true,
    label: '条件',
    type: 'select',
    condition: '=',
    dictKey: 'higth_query_condition',
    commonType: 'dict'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'common_type',
    default: true,
    label: '组件类型',
    type: 'select',
    condition: '=',
    dictKey: 'hight_query_compoent',
    commonType: 'dict'
  },
  {
    andOr: 'and',
    sortNum: 1,
    isDisable: false,
    value: '',
    ruleAlias: 't',
    column: 'busi_key',
    default: true,
    label: '业务主键',
    type: 'string',
    condition: 'like',
    commonType: 'input'
  }
]
