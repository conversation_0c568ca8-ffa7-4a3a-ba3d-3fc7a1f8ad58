<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>
          {{ $t('intl.drawer.basicInfo') }}
        </template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('condition')" prop="andOr">
              <dict-tag :options="and_or" :value="form.busiKey" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('sort')" prop="sortNum">
              {{ form.sortNum }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('disabled.question')" prop="isDisable">
              <dict-tag :options="sys_yes_no" :value="form.busiKey" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('alias.table')" prop="ruleAlias">
              {{ form.ruleAlias }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('name.column')" prop="column">
              {{ form.column }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('default.question')" prop="isDefault">
              {{ form.isDefault }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('name.label')" prop="label">
              {{ form.label }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('type')" prop="type">
              <dict-tag :options="higth_query_type" :value="form.busiKey" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('condition')" prop="condition">
              <dict-tag :options="higth_query_condition" :value="form.busiKey" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('type.component')" prop="commonType">
              <dict-tag :options="hight_query_compoent" :value="form.busiKey" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('key.business')" prop="busiKey">
              {{ form.busiKey }}
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, getCurrentInstance, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getHigthQueryDetail } from '@/api/system/higthQueryDetail/HigthQueryDetail'

  const { proxy } = getCurrentInstance()

  const { higth_query_condition, and_or, sys_yes_no, hight_query_compoent, higth_query_type } =
    proxy.useDict(
      'higth_query_condition',
      'and_or',
      'sys_yes_no',
      'hight_query_compoent',
      'higth_query_type'
    )
  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    if (props.id) {
      getHigthQueryDetail(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
