<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      :options="options"
      @load="search"
      @refresh="refresh"
      @search="search"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:higthQueryDetail:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:higthQueryDetail:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:higthQueryDetail:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:higthQueryDetail:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="higthQueryDetailList"
      height="540px"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column :label="$t('condition')" :show-overflow-tooltip="true" prop="andOr">
        <template #default="scope">
          <dict-tag :options="and_or" :value="scope.row.andOr" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('sort')" :show-overflow-tooltip="true" prop="sortNum" />
      <el-table-column
        :label="$t('disabled.question')"
        :show-overflow-tooltip="true"
        prop="isDisable"
      >
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isDisable" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('alias.table')" :show-overflow-tooltip="true" prop="ruleAlias" />
      <el-table-column :label="$t('name.column')" :show-overflow-tooltip="true" prop="column" />
      <el-table-column
        :label="$t('default.question')"
        :show-overflow-tooltip="true"
        prop="isDefault"
      />
      <el-table-column :label="$t('name.label')" :show-overflow-tooltip="true" prop="label" />
      <el-table-column :label="$t('type')" :show-overflow-tooltip="true" prop="type">
        <template #default="scope">
          <dict-tag :options="higth_query_type" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('condition')" :show-overflow-tooltip="true" prop="condition">
        <template #default="scope">
          <dict-tag :options="higth_query_condition" :value="scope.row.condition" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('type.component')"
        :show-overflow-tooltip="true"
        prop="commonType"
      >
        <template #default="scope">
          <dict-tag :options="hight_query_compoent" :value="scope.row.commonType" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('key.business')" :show-overflow-tooltip="true" prop="busiKey" />
      <el-table-column :label="$t('operation')" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:higthQueryDetail:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:higthQueryDetail:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('action.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNum"
      :total="total"
      @pagination="search"
    />

    <!-- 添加或修改高级查询列对话框 -->

    <add
      :id="id"
      v-model:show="open"
      :title="title"
      @refreshList="refreshList"
      @close="handleClose"
    />

    <!-- 详情高级查询列对话框 -->
    <detail :id="id" v-model:show="detailShow" :title="title" @close="handleDetailClose" />
  </div>
</template>

<script name="HigthQueryDetail" setup>
  import { getCurrentInstance } from 'vue'
  import {
    delHigthQueryDetail,
    listHigthQueryDetail
  } from '@/api/system/higthQueryDetail/HigthQueryDetail'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'
  import options from './components/listOptions.js'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'

  const { proxy } = getCurrentInstance()

  const { higth_query_condition, and_or, sys_yes_no, hight_query_compoent, higth_query_type } =
    proxy.useDict(
      'higth_query_condition',
      'and_or',
      'sys_yes_no',
      'hight_query_compoent',
      'higth_query_type'
    )

  const higthQueryDetailList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const id = ref()
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    fromKey: 't_higth_query_detail',
    fromName: proxy.$t('columns.search.advanced'),
    deptList: [],
    userList: [],

    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listHigthQueryDetail(data.queryParams)
      .then(({ data }) => {
        higthQueryDetailList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.andOr)

    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    handleClose()
    nextTick(() => {
      id.value = undefined
      open.value = true
      title.value = proxy.$t('column.search.advanced.add')
    })
  }

  const handleClose = () => {
    open.value = false
    id.value = undefined
    title.value = ''
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.id || ids.value
    title.value = proxy.$t('column.search.advanced.modify')
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.andOr || names.value
    proxy.$modal
      .confirm(`是否确认删除条件为【${nameArr}】的数据项?`)
      .then(function () {
        return delHigthQueryDetail(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/higthQueryDetail/export',
      {
        ...queryParams.value
      },
      `higthQueryDetail_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }

  const visibleDetail = (row) => {
    nextTick(() => {
      id.value = row.id
      title.value = proxy.$t('columns.search.advanced.view')
      detailShow.value = true
    })
  }
  /** 处理详情弹窗关闭 */
  const handleDetailClose = () => {
    detailShow.value = false
    id.value = undefined
    title.value = ''
  }
</script>
