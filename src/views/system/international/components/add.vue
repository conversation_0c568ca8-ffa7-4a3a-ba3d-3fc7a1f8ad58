<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button type="primary" @click="submitForm" :loading="loadingBtn.submit"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="internationalRef" :model="form" :rules="rules">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('intl.form.title')" prop="title">
                <el-input v-model="form.title" :placeholder="$t('intl.form.title.placeholder')" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('intl.form.lang')" prop="lang">
                <el-select
                  v-model="form.lang"
                  :placeholder="$t('intl.form.lang.placeholder')"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in lang_i18n"
                    :key="dict.value"
                    :label="$t(dict.type + '.' + dict.value)"
                    :value="dict.value"
                  >
                    <el-tag :type="dict.elTagType">
                      {{ $t(dict.type + '.' + dict.value) }}
                    </el-tag>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('intl.form.code')" prop="code">
                <el-input v-model="form.code" :placeholder="$t('intl.form.code.placeholder')" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, defineEmits, defineProps, getCurrentInstance, reactive, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import {
    addInternational,
    getInternational,
    updateInternational
  } from '@/api/system/international/International'

  const { proxy } = getCurrentInstance()

  const { lang_i18n } = proxy.useDict('lang_i18n')
  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: { lang: 'zh' },
    rules: {
      title: [
        { required: true, message: proxy.$t('intl.validation.title.empty'), trigger: 'blur' }
      ],
      lang: [
        { required: true, message: proxy.$t('intl.validation.lang.empty'), trigger: 'change' }
      ],
      code: [{ required: true, message: proxy.$t('intl.validation.code.empty'), trigger: 'blur' }]
    }
  })

  const loadingBtn = reactive({
    submit: false
  })
  const { form, rules } = toRefs(data)

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['internationalRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateInternational(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const add = () => {
    addInternational(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getInternational(props.id).then((response) => {
        Object.assign(data.form, response.data)
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      title: null,
      lang: 'zh',
      code: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null
    }
    proxy.resetForm('internationalRef')
  }
</script>
