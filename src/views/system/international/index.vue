<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="search"
      @refresh="refresh"
      @search="search"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:international:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:international:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:international:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:international:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:international:translate']"
          icon="Download"
          plain
          type="warning"
          @click="handleTranslate"
          >{{ $t('btn.translate') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="internationalList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="search"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column :label="$t('table.title')" :show-overflow-tooltip="true" prop="title">
        <template #default="{ row }">
          <el-link type="primary" @click="visibleDetail(row)">{{ row.title }}</el-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.language')" :show-overflow-tooltip="true" prop="lang">
        <template #default="scope">
          <dict-tag :options="lang_i18n" :value="scope.row.lang" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.code')" :show-overflow-tooltip="true" prop="code" />
      <el-table-column
        :label="$t('table.operation')"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:international:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:international:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <add :id="id" v-model:show="open" :title="$t('dialog.add')" @refreshList="refreshList" />
    <detail :id="id" v-model:show="detailShow" :title="$t('dialog.detail')" />
  </div>
</template>

<script name="International" setup>
  import { getCurrentInstance } from 'vue'
  import {
    delInternational,
    listInternational,
    translateDict
  } from '@/api/system/international/International'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'

  const { proxy } = getCurrentInstance()

  const { lang_i18n } = proxy.useDict('lang_i18n')

  const internationalList = ref([])
  const options = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const showSearch = ref(true)
  const ids = ref([])
  const id = ref()
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    fromKey: 't_international',
    fromName: proxy.$t('app.internationalization'), // 国际化
    deptList: [],
    userList: [],
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({ nickName: item }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({ deptName: item }).then((res) => {
      data.deptList = res.data
    })
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listInternational(data.queryParams)
      .then(({ data }) => {
        internationalList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.title)
    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    id.value = undefined
    open.value = true
    title.value = proxy.$t('dialog.add') // 添加国际化
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.id || ids.value
    title.value = proxy.$t('dialog.edit') // 修改国际化
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.title || names.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete', { name: nameArr }))
      .then(() => {
        return delInternational(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/international/export',
      {
        ...queryParams.value
      },
      `international_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }
  const handleTranslate = () => {
    translateDict().then(() => {
      proxy.$modal.msgSuccess(proxy.$t('operation.success'))
    })
  }
  const visibleDetail = (row) => {
    id.value = row.id
    title.value = proxy.$t('dialog.detail') // 查看国际化
    detailShow.value = true
  }
</script>
