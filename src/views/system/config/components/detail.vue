<template>
  <Drawer v-model="open" @open="init" :size="600">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form label-width="140px" style="width: 100%;">
          <el-row :gutter="0">
            <!-- 参数主键 -->
            <el-col :span="12">
              <el-form-item :label="$t('key.primary.parameter')">
                <span>{{ form.configId }}</span>
              </el-form-item>
            </el-col>
            <!-- 参数名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.parameter')">
                <span>{{ form.configName }}</span>
              </el-form-item>
            </el-col>
            <!-- 参数键名 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.key.parameter')">
                <span>{{ form.configKey }}</span>
              </el-form-item>
            </el-col>
            <!-- 参数键值 -->
            <el-col :span="12">
              <el-form-item :label="$t('key.value.parameter')">
                <span>{{ form.configValue }}</span>
              </el-form-item>
            </el-col>
            <!-- 系统内置 -->
            <el-col :span="12">
              <el-form-item :label="$t('built.in.system')">
                <dict-tag :options="sys_yes_no" :value="form.configType" />
              </el-form-item>
            </el-col>
            <!-- 创建时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.creation')">
                <span>{{ parseTime(form.createTime) }}</span>
              </el-form-item>
            </el-col>
            <!-- 更新时间 -->
            <el-col :span="12">
              <el-form-item :label="$t('time.update')">
                <span>{{ parseTime(form.updateTime) }}</span>
              </el-form-item>
            </el-col>
            <!-- 备注 -->
            <el-col v-if="form.remark" :span="24">
              <el-form-item :label="$t('remarks')">
                <span>{{ form.remark || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getConfig } from '@/api/system/config'

  const { proxy } = getCurrentInstance()
  const { sys_yes_no } = proxy.useDict('sys_yes_no')

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    reset()
    if (props.id) {
      getConfig(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {}
  }
</script>

<style scoped>
/* 组件样式 */
</style>
