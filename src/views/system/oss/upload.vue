<template>
  <div class="upload-page">
    <div class="upload-container">
      <el-form>
        <el-form-item label="上传至分组">
          <category-cascader
            v-model="form.groupId"
            :include-all="true"
            filterable
            placeholder="请选择分组"
            type="upload_img"
            @change="handleCategoryChange"
          />
        </el-form-item>
      </el-form>
      <input
        ref="fileInput"
        accept="image/*"
        multiple
        style="display: none"
        type="file"
        @change="handleFileChange"
      />
      <div class="image-list">
        <div v-for="(file, index) in fileList" :key="index" class="image-preview">
          <img :src="file.url" alt="预览图" class="preview-image" />
          <div class="image-actions">
            <el-icon class="action-icon" @click.stop="handlePreview(file)">
              <ZoomIn />
            </el-icon>
            <el-icon class="action-icon" @click.stop="handleRemove(file)">
              <Delete />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="upload-button-wrapper">
      <div class="button-group">
        <el-button
          :icon="Plus"
          class="upload-button"
          size="large"
          type="primary"
          @click="handleChoose"
        >
          选择图片
        </el-button>
        <el-button
          :icon="Upload"
          class="upload-button"
          size="large"
          type="success"
          @click="handleUpload"
        >
          开始上传
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import CategoryCascader from '@/components/CategoryCascader.vue'
  import { Delete, Plus, Upload, ZoomIn } from '@element-plus/icons-vue'
  import { ElImageViewer } from 'element-plus'
  import { getCurrentInstance, ref } from 'vue'
  import { useRoute } from 'vue-router'

  const { proxy } = getCurrentInstance()

  const fileInput = ref(null)
  const fileList = ref([])

  const route = useRoute()
  const queryParams = route.query

  const form = ref({
    groupId: queryParams.categoryId ? BigInt(queryParams.categoryId).toString() : '0'
  })

  const handleCategoryChange = (value) => {
    console.log('分组已更改:', value)
    form.value.groupId = value
  }

  const handlePreview = (file) => {
    const instance = ElImageViewer({
      urlList: [file.url],
      onClose: () => {
        instance.close()
      }
    })
  }

  const handleRemove = (file) => {
    const index = fileList.value.indexOf(file)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
  }

  const handleFileChange = (event) => {
    const files = Array.from(event.target.files)
    files.forEach((file) => {
      const uploadFile = {
        raw: file,
        name: file.name,
        url: URL.createObjectURL(file)
      }
      fileList.value.push(uploadFile)
    })
    event.target.value = '' // 清空input，允许重复选择相同文件
  }

  const handleChoose = () => {
    fileInput.value.click()
  }

  const handleUpload = async () => {
    if (fileList.value.length === 0) {
      proxy.$msg.warning('请先选择要上传的图片')
      return
    }

    let fileArr = []
    fileList.value.forEach((file) => {
      fileArr.push(file.raw)
    })

    try {
      let { code } = await proxy.$upload(fileArr, form.value.groupId, queryParams.socketId)
      console.log('上传结果:', code)
      if (code !== 200) {
        proxy.$msg.error('上传失败')
        return
      }
      proxy.$msg.success('上传成功')
      fileList.value = []
    } catch (error) {
      console.error('上传出错:', error)
      proxy.$msg.error('上传失败: ' + error.message)
    }
  }
</script>

<style scoped>
  .upload-page {
    padding: 8px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 76px;
  }

  .upload-container {
    flex: 1;
    /* display: flex; */
    flex-wrap: wrap;
    gap: 8px;
    align-content: flex-start;
  }

  .upload-container :deep(.el-upload) {
    display: none !important;
  }

  .upload-container :deep(.el-upload-list) {
    display: none !important;
  }

  .image-preview {
    width: 100px;
    height: 100px;
    position: relative;
    border-radius: 4px;
    overflow: hidden;
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s;
  }

  .image-actions {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s;
  }

  .image-preview:hover .image-actions {
    opacity: 1;
  }

  .action-icon {
    padding: 8px;
    font-size: 20px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
  }

  .action-icon:hover {
    transform: scale(1.1);
    background-color: rgba(0, 0, 0, 0.7);
  }

  .upload-button-wrapper {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 8px;
    background-color: #fff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  .button-group {
    display: flex;
    gap: 8px;
  }

  .upload-button {
    flex: 1;
    margin: 0;
  }

  @media screen and (max-width: 768px) {
    .upload-container :deep(.el-upload--picture-card),
    .upload-container :deep(.el-upload-list__item) {
      width: 80px !important;
      height: 80px !important;
    }

    .image-preview {
      width: 80px;
      height: 80px;
    }
  }

  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
</style>
