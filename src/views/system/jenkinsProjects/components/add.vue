<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm"
          >{{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>
          {{ $t('intl.drawer.basicInfo') }}
        </template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="jenkinsProjectsRef" :model="form" :rules="rules" label-width="140px">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('name.project')" prop="name">
                <el-input v-model="form.name" :placeholder="$t('name.project.input')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('name.project.jenkins')" prop="jenkinsName">
                <el-input
                  v-model="form.jenkinsName"
                  :placeholder="$t('name.project.jenkins.input')"
                />
              </el-form-item>
            </el-col>

            <!--<el-col :span="12">-->
            <!--  <el-form-item label="git仓库" prop="gitAddr">-->
            <!--    <el-input v-model="form.gitAddr" placeholder="请输入git仓库"/>-->
            <!--  </el-form-item>-->
            <!--</el-col>-->

            <el-col :span="12">
              <el-form-item :label="$t('branch')" prop="branch">
                <el-input v-model="form.branch" :placeholder="$t('branch.input')" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item :label="$t('address.jenkins')" prop="remoteUrl">
                <el-input v-model="form.remoteUrl" :placeholder="$t('address.jenkins.input')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Token" prop="jenkinsToken">
                <el-input v-model="form.jenkinsToken" :placeholder="$t('token.input')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('work.status')" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="item in sys_normal_disable"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ $t(`${item.type}.${item.value}`) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, getCurrentInstance, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'
  import {
    addJenkinsProjects,
    getJenkinsProjects,
    updateJenkinsProjects
  } from '@/api/system/jenkinsProjects/JenkinsProjects'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {},
    rules: {
      name: [{ required: true, message: proxy.$t('name.project.empty'), trigger: 'blur' }],
      jenkinsToken: [{ required: true, message: proxy.$t('token.empty'), trigger: 'blur' }],
      jenkinsName: [{ required: true, message: proxy.$t('name.jenkins.empty'), trigger: 'blur' }],
      branch: [{ required: true, message: proxy.$t('branch.empty'), trigger: 'blur' }],
      remoteUrl: [{ required: true, message: proxy.$t('address.jenkins.empty'), trigger: 'blur' }]
    }
  })

  const { form, rules } = toRefs(data)
  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['jenkinsProjectsRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true

        if (form.value.id != null) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updateJenkinsProjects(form.value)
      .then((response) => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }
  const add = () => {
    addJenkinsProjects(form.value)
      .then((response) => {
        console.log(proxy.$modal)
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getJenkinsProjects(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      id: null,
      name: null,
      gitAddr: null,
      branch: null,
      remoteUrl: null,
      status: '0'
    }
    proxy.resetForm('jenkinsProjectsRef')
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }
</script>
