<template>
  <Drawer v-model="open" @open="init">
    <template #title>
      <p>{{ title }}</p>
    </template>
    <template #content>
      <DrawerTitle>
        <template #default>
          {{ $t('intl.drawer.basicInfo') }}
        </template>
      </DrawerTitle>
      <DrawerGroup>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('name.project.git')" prop="name">
              {{ form.name }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('name.project.jenkins')" prop="jenkinsName">
              {{ form.jenkinsName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('branch')" prop="branch">
              {{ form.branch }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('address.jenkins')" prop="remoteUrl">
              {{ form.remoteUrl }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Token" prop="jenkinsToken">
              {{ form.jenkinsToken }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('work.status')" prop="status">
              <dict-tag :options="sys_normal_disable" :value="form.status" />
            </el-form-item>
          </el-col>
        </el-row>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, getCurrentInstance, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { getJenkinsProjects } from '@/api/system/jenkinsProjects/JenkinsProjects'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const emits = defineEmits(['update:show'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: String,
      default: ''
    }
  })
  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })
  const data = reactive({
    form: {}
  })

  const { form } = toRefs(data)

  const init = () => {
    data.form = {}
    if (props.id) {
      getJenkinsProjects(props.id).then((response) => {
        data.form = response.data
      })
    }
  }
</script>
