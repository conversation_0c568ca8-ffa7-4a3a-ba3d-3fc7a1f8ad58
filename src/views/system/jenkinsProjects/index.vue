<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="search"
      @refresh="refresh"
      @search="search"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:jenkinsProjects:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:jenkinsProjects:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:jenkinsProjects:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:jenkinsProjects:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="jenkinsProjectsList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      @search="search"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column :label="$t('name.project')" :show-overflow-tooltip="true" prop="name">
        <template #default="{ row }">
          <el-link type="primary" @click="visibleDetail(row)">{{ row.name }}</el-link>
        </template>
      </el-table-column>
      <!--<el-table-column label="git仓库" prop="gitAddr" :show-overflow-tooltip="true"/>-->
      <el-table-column :label="$t('branch')" :show-overflow-tooltip="true" prop="branch" />
      <el-table-column
        :label="$t('name.jenkins')"
        :show-overflow-tooltip="true"
        prop="jenkinsName"
      />
      <el-table-column
        :label="$t('address.jenkins')"
        :show-overflow-tooltip="true"
        prop="remoteUrl"
      />
      <el-table-column :show-overflow-tooltip="true" label="Token" prop="jenkinsToken" />
      <el-table-column :label="$t('work.status')" :show-overflow-tooltip="true" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('operation')" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:jenkinsProjects:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            >{{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:jenkinsProjects:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('action.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改jenkins项目对话框 -->

    <add
      :id="id"
      v-model:show="open"
      :title="title"
      @refreshList="refreshList"
      @close="handleClose"
    />

    <!-- 详情jenkins项目对话框 -->
    <detail :id="id" v-model:show="detailShow" :title="title" @close="handleDetailClose" />
  </div>
</template>

<script name="JenkinsProjects" setup>
  import { getCurrentInstance } from 'vue'
  import {
    delJenkinsProjects,
    listJenkinsProjects
  } from '@/api/system/jenkinsProjects/JenkinsProjects'
  import Add from './components/add.vue'
  import Detail from './components/detail.vue'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import { listUser } from '@/api/system/user'
  import { listDept } from '@/api/system/dept'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const jenkinsProjectsList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const loading = ref(true)
  const ids = ref([])
  const id = ref()
  const names = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    fromKey: 't_jenkins_projects',
    fromName: proxy.$t('project.jenkins'),
    deptList: [],
    userList: [],

    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20
    },
    rules: {
      name: [{ required: true, message: proxy.$t('name.project.empty'), trigger: 'blur' }]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  function refresh(query) {
    query.pageNum = 1
    search(query)
  }

  const handleUserSelect = (item) => {
    if (item === '') {
      data.userList = []
      return
    }
    listUser({
      nickName: item
    }).then((res) => {
      data.userList = res.rows
    })
  }

  const handleDeptSelect = (item) => {
    if (item === '') {
      data.deptList = []
      return
    }
    listDept({
      deptName: item
    }).then((res) => {
      data.deptList = res.data
    })
  }

  function search(query) {
    if (query) {
      data.queryParams = { ...data.queryParams, ...query }
    }
    loading.value = true
    listJenkinsProjects(data.queryParams)
      .then(({ data }) => {
        jenkinsProjectsList.value = data.records
        total.value = data.totalRow
        loading.value = false
      })
      .catch((err) => {
        loading.value = false
      })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    reset()
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id)
    names.value = selection.map((item) => item.name)

    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    handleClose()
    nextTick(() => {
      id.value = undefined
      open.value = true
      title.value = proxy.$t('project.jenkins.add')
    })
  }

  const handleClose = () => {
    open.value = false
    id.value = undefined
    title.value = ''
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    id.value = row.id || ids.value
    title.value = proxy.$t('project.jenkins.modify')
    open.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const idKeys = row.id || ids.value
    const nameArr = row.name || names.value
    console.log(proxy)
    proxy.$modal
      .confirm(`是否确认删除项目名称为【${nameArr}】的数据项?`)
      .then(function () {
        return delJenkinsProjects(idKeys)
      })
      .then(() => {
        search()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/jenkinsProjects/export',
      {
        ...queryParams.value
      },
      `jenkinsProjects_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = (pageNum) => {
    if (pageNum) {
      data.queryParams.pageNum = 1
    }
    search()
  }

  const visibleDetail = (row) => {
    nextTick(() => {
      id.value = row.id
      title.value = proxy.$t('project.jenkins.view')
      detailShow.value = true
    })
  }
  /** 处理详情弹窗关闭 */
  const handleDetailClose = () => {
    detailShow.value = false
    id.value = undefined
    title.value = ''
  }
</script>
