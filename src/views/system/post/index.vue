<template>
  <div class="page-content">
    <high-query
      :formKey="data.fromKey"
      :formName="data.fromName"
      @load="getList"
      @refresh="refresh"
      @search="getList"
    />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:add']"
          icon="Plus"
          plain
          type="primary"
          @click="handleAdd"
          >{{ $t('button.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:edit']"
          :disabled="single"
          icon="Edit"
          plain
          type="success"
          @click="handleUpdate"
          >{{ $t('action.modify') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:remove']"
          :disabled="multiple"
          icon="Delete"
          plain
          type="danger"
          @click="handleDelete"
          >{{ $t('button.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:post:export']"
          icon="Download"
          plain
          type="warning"
          @click="handleExport"
          >{{ $t('button.export') }}
        </el-button>
      </el-col>
    </el-row>

    <art-table
      v-loading="loading"
      :data="postList"
      v-model:page-num="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      :total="total"
      row-key="postId"
      @search="getList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column :label="$t('number.post')" align="center" prop="postId" />
      <el-table-column :label="$t('code.post')" align="center" prop="postCode" />
      <el-table-column :label="$t('name.post')" align="center" prop="postName">
        <template #default="{ row }">
          <el-link type="primary" @click="handleDetail(row)">{{ row.postName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('sort.post')" align="center" prop="postSort" />
      <el-table-column :label="$t('status')" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('time.creation')" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('operation')"
        align="center"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['system:post:edit']"
            icon="Edit"
            link
            type="primary"
            @click="handleUpdate(scope.row)"
          >
            {{ $t('action.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:post:remove']"
            icon="Delete"
            link
            type="danger"
            @click="handleDelete(scope.row)"
            >{{ $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </art-table>

    <!-- 添加或修改岗位对话框 -->
    <add v-model:show="open" :title="title" :id="postId" @refreshList="refreshList" />

    <!-- 岗位详情对话框 -->
    <detail v-model:show="detailShow" :title="title" :id="postId" />
  </div>
</template>

<script name="Post" setup>
  import { getCurrentInstance } from 'vue'
  import { delPost, listPost } from '@/api/system/post'
  import HighQuery from '@/components/HighQuery/HighQuery.vue'
  import add from './components/add.vue'
  import detail from './components/detail.vue'

  const { proxy } = getCurrentInstance()

  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const postList = ref([])
  const open = ref(false)
  const detailShow = ref(false)
  const postId = ref('')
  const loading = ref(true)
  const ids = ref([])
  const single = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const title = ref('')

  const data = reactive({
    fromKey: 'post',
    fromName: 'post',
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      postCode: undefined,
      postName: undefined,
      status: undefined
    }
  })

  const { queryParams } = toRefs(data)

  /** 查询岗位列表 */
  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    loading.value = true
    listPost(queryParams.value).then(({ data }) => {
      postList.value = data.records
      total.value = data.totalRow
      loading.value = false
    })
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.postId)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  /** 新增按钮操作 */
  function handleAdd() {
    postId.value = ''
    title.value = proxy.$t('post.add')
    open.value = true
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    postId.value = row.postId || ids.value
    title.value = proxy.$t('post.modify')
    open.value = true
  }

  /** 查看详情操作 */
  function handleDetail(row) {
    postId.value = row.postId
    title.value = proxy.$t('post.detail')
    detailShow.value = true
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const postIds = row.postId || ids.value
    proxy.$modal
      .confirm(proxy.$t('confirm.delete.number.post') + postIds + proxy.$t('item.data.question'))
      .then(function () {
        return delPost(postIds)
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess(proxy.$t('message.deleteSuccess'))
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/post/export',
      {
        ...queryParams.value
      },
      `post_${new Date().getTime()}.xlsx`
    )
  }

  const refreshList = () => {
    getList()
  }

  const refresh = () => {
    getList()
  }

  getList()
</script>
