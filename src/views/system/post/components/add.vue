<template>
  <Drawer v-model="open" @open="init" :size="600">
    <template #title>
      <p>{{ title }}</p>
    </template>

    <template #btn_group>
      <el-row>
        <el-button :loading="loadingBtn.submit" type="primary" @click="submitForm">
          {{ $t('intl.btn.submit') }}
        </el-button>
      </el-row>
    </template>

    <template #content>
      <DrawerTitle>
        <template #default>{{ $t('intl.drawer.basicInfo') }}</template>
      </DrawerTitle>
      <DrawerGroup>
        <el-form ref="postRef" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <!-- 岗位名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('name.post')" prop="postName">
                <el-input v-model="form.postName" :placeholder="$t('name.post.input')" />
              </el-form-item>
            </el-col>

            <!-- 岗位编码 -->
            <el-col :span="12">
              <el-form-item :label="$t('code.post')" prop="postCode">
                <el-input v-model="form.postCode" :placeholder="$t('name.code.input')" />
              </el-form-item>
            </el-col>

            <!-- 岗位顺序 -->
            <el-col :span="12">
              <el-form-item :label="$t('sequence.post')" prop="postSort">
                <el-input-number
                  v-model="form.postSort"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <!-- 岗位状态 -->
            <el-col :span="12">
              <el-form-item :label="$t('status.post')" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio
                    v-for="dict in sys_normal_disable"
                    :key="dict.value"
                    :value="dict.value"
                  >
                    {{ $t(dict.type + '.' + dict.value) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <!-- 备注 -->
            <el-col :span="24">
              <el-form-item :label="$t('remarks')" prop="remark">
                <el-input
                  v-model="form.remark"
                  :placeholder="$t('content.input')"
                  type="textarea"
                  :rows="3"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </DrawerGroup>
    </template>
  </Drawer>
</template>

<script setup>
  import { computed, toRefs } from 'vue'
  import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
  import Drawer from '@/components/Drawer/Drawer.vue'
  import DrawerGroup from '@/components/Drawer-Group/DrawerGroup.vue'
  import { addPost, getPost, updatePost } from '@/api/system/post'

  const { proxy } = getCurrentInstance()
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

  const emits = defineEmits(['update:show', 'refreshList'])
  const props = defineProps({
    title: String,
    show: Boolean,
    id: {
      type: [String, Number],
      default: ''
    }
  })

  const open = computed({
    get: () => props.show,
    set: (val) => {
      emits('update:show', val)
    }
  })

  const data = reactive({
    form: {},
    rules: {
      postName: [{ required: true, message: proxy.$t('name.post.empty'), trigger: 'blur' }],
      postCode: [{ required: true, message: proxy.$t('code.post.empty'), trigger: 'blur' }],
      postSort: [{ required: true, message: proxy.$t('sequence.post.empty'), trigger: 'blur' }]
    }
  })

  const { form, rules } = toRefs(data)

  const loadingBtn = reactive({
    submit: false
  })

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['postRef'].validate((valid) => {
      if (valid) {
        loadingBtn.submit = true
        if (form.value.postId !== undefined) {
          update()
        } else {
          add()
        }
      }
    })
  }

  const update = () => {
    updatePost(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('intl.message.updateSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const add = () => {
    addPost(form.value)
      .then(() => {
        proxy.$modal.msgSuccess(proxy.$t('common.message.addSuccess'))
        emits('refreshList')
        open.value = false
      })
      .finally(() => {
        loadingBtn.submit = false
      })
  }

  const init = () => {
    reset()
    if (props.id) {
      getPost(props.id).then((response) => {
        data.form = response.data
      })
    }
  }

  // 表单重置
  function reset() {
    form.value = {
      postId: undefined,
      postCode: undefined,
      postName: undefined,
      postSort: 0,
      status: '0',
      remark: undefined
    }
    proxy.resetForm('postRef')
  }
</script>

<style scoped>
  .el-input-number {
    width: 100%;
  }
</style>
