<template>
  <!-- 创建表 -->
  <el-dialog v-model="visible" :title="$t('table.create')" append-to-body top="5vh" width="800px">
    <span>{{ $t('statement.table.create.multiple') }}</span>
    <el-input
      v-model="content"
      :placeholder="$t('text.input')"
      :rows="10"
      type="textarea"
    ></el-input>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleImportTable">{{ $t('action.confirm') }}</el-button>
        <el-button @click="visible = false">{{ $t('common.cancel') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'
  import { createTable } from '@/api/tool/gen'

  const { proxy } = getCurrentInstance()

  const visible = ref(false)
  const content = ref('')
  const emit = defineEmits(['ok'])

  /** 显示弹框 */
  function show() {
    visible.value = true
  }

  /** 导入按钮操作 */
  function handleImportTable() {
    if (content.value === '') {
      proxy.$modal.msgError(proxy.$t('statement.table.create'))
      return
    }
    createTable({ sql: content.value }).then((res) => {
      proxy.$modal.msgSuccess(res.msg)
      if (res.code === 200) {
        visible.value = false
        emit('ok')
      }
    })
  }

  defineExpose({
    show
  })
</script>
