<template>
  <el-form ref="basicInfoForm" :model="info" :rules="rules" label-width="150px">
    <el-row>
      <el-col :span="12">
        <el-form-item :label="$t('name.table')" prop="tableName">
          <el-input v-model="info.tableName" :placeholder="$t('name.repository.input')" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('description.table')" prop="tableComment">
          <el-input v-model="info.tableComment" :placeholder="$t('input.please')" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('name.class.entity')" prop="className">
          <el-input v-model="info.className" :placeholder="$t('input.please')" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="$t('author')" prop="functionAuthor">
          <el-input v-model="info.functionAuthor" :placeholder="$t('input.please')" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item :label="$t('remarks')" prop="remark">
          <el-input v-model="info.remark" :rows="3" type="textarea"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'

  const { proxy } = getCurrentInstance()
  defineProps({
    info: {
      type: Object,
      default: null
    }
  })

  // 表单校验
  const rules = ref({
    tableName: [{ required: true, message: proxy.$t('name.table.input'), trigger: 'blur' }],
    tableComment: [
      { required: true, message: proxy.$t('description.table.input'), trigger: 'blur' }
    ],
    className: [{ required: true, message: proxy.$t('name.class.entity.input'), trigger: 'blur' }],
    functionAuthor: [{ required: true, message: proxy.$t('author.input'), trigger: 'blur' }]
  })
</script>
