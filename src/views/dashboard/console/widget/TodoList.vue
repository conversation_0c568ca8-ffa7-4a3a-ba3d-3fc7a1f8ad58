<template>
  <div class="card art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">{{ $t('items.to.do') }}</h4>
        <p class="subtitle">{{ $t('status.pending') }}<span>+6</span></p>
      </div>
    </div>

    <div class="list">
      <div v-for="(item, index) in list" :key="index">
        <p class="title">{{ item.username }}</p>
        <p class="date subtitle">{{ item.date }}</p>
        <el-checkbox v-model="item.complate" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getCurrentInstance } from 'vue'
  import { reactive } from 'vue-demi'

  const { proxy } = getCurrentInstance()

  const list = reactive([
    {
      username: proxy.$t('content.work.today'),
      date: proxy.$t('time.am.09.30'),
      complate: true
    },
    {
      username: proxy.$t('email.reply'),
      date: proxy.$t('time.am.10.30'),
      complate: true
    },
    {
      username: proxy.$t('organization.report.work'),
      date: proxy.$t('time.am.11.00'),
      complate: true
    },
    {
      username: proxy.$t('meeting.requirements.product'),
      date: proxy.$t('time.pm.02.00'),
      complate: false
    },
    {
      username: proxy.$t('content.meeting.organize'),
      date: proxy.$t('time.pm.03.30'),
      complate: false
    },
    {
      username: proxy.$t('plan.work.tomorrow'),
      date: proxy.$t('time.pm.06.30'),
      complate: false
    }
  ])
</script>

<style lang="scss" scoped>
  .card {
    box-sizing: border-box;
    width: 100%;
    height: 510px;
    padding: 0 25px;

    .list {
      height: calc(100% - 90px);
      margin-top: 10px;
      overflow: hidden;

      > div {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 70px;
        overflow: hidden;
        border-bottom: 1px solid var(--art-border-color);

        p {
          font-size: 13px;
        }

        .title {
          font-size: 14px;
        }

        .date {
          margin-top: 6px;
          font-size: 12px;
          font-weight: 400;
        }

        .el-checkbox {
          position: absolute;
          top: 0;
          right: 10px;
          bottom: 0;
          margin: auto;
        }
      }
    }
  }
</style>
