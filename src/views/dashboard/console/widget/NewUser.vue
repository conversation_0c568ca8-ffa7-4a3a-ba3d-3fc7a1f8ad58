<template>
  <div class="card art-custom-card">
    <div class="card-header">
      <div class="title">
        <h4 class="box-title">{{ $t('user.new') }}</h4>
        <p class="subtitle">{{ $t('growth.month.this') }}<span>+20%</span></p>
      </div>
      <el-radio-group v-model="radio2">
        <el-radio-button
          :label="$t('date.this.month')"
          :value="$t('date.this.month')"
        ></el-radio-button>
        <el-radio-button
          :label="$t('date.last.month')"
          :value="$t('date.last.month')"
        ></el-radio-button>
        <el-radio-button
          :label="$t('date.this.year')"
          :value="$t('date.this.year')"
        ></el-radio-button>
      </el-radio-group>
    </div>
    <el-table
      class="table"
      :data="tableData"
      :pagination="false"
      size="large"
      :border="false"
      :stripe="false"
      :show-header-background="false"
    >
      <template #default>
        <el-table-column :label="$t('avatar')" prop="avatar" width="150px">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              <img class="avatar" :src="scope.row.avatar" alt="avatar" />
              <span class="user-name">{{ scope.row.username }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('region')" prop="province" />
        <el-table-column :label="$t('gender')" prop="avatar">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              <span style="margin-left: 10px">{{ $t('gender.dynamic') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('age')" prop="age" />
        <el-table-column :label="$t('progress')">
          <template #default="scope">
            <el-progress :color="scope.row.color" :percentage="scope.row.pro" :stroke-width="4" />
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, reactive } from 'vue-demi'
  import avatar1 from '@/assets/img/avatar/avatar1.webp'
  import avatar2 from '@/assets/img/avatar/avatar2.webp'
  import avatar3 from '@/assets/img/avatar/avatar3.webp'
  import avatar4 from '@/assets/img/avatar/avatar4.webp'
  import avatar5 from '@/assets/img/avatar/avatar5.webp'
  import avatar6 from '@/assets/img/avatar/avatar6.webp'
  import { getCurrentInstance } from 'vue'

  const { proxy } = getCurrentInstance()

  const radio2 = ref(proxy.$t('date.this.month'))

  const tableData = reactive([
    {
      username: proxy.$t('fish.medium'),
      province: proxy.$t('location.beijing'),
      sex: 0,
      age: 22,
      percentage: 60,
      pro: 0,
      color: 'rgb(var(--art-primary)) !important',
      avatar: avatar1
    },
    {
      username: proxy.$t('he.xiaohe'),
      province: proxy.$t('location.shenzhen'),
      sex: 1,
      age: 21,
      percentage: 20,
      pro: 0,
      color: 'rgb(var(--art-secondary)) !important',
      avatar: avatar2
    },
    {
      username: proxy.$t('duo.duo.yin'),
      province: proxy.$t('location.shanghai'),
      sex: 1,
      age: 23,
      percentage: 60,
      pro: 0,
      color: 'rgb(var(--art-warning)) !important',
      avatar: avatar3
    },
    {
      username: proxy.$t('grass.daydreaming'),
      province: proxy.$t('location.changsha'),
      sex: 0,
      age: 28,
      percentage: 50,
      pro: 0,
      color: 'rgb(var(--art-info)) !important',
      avatar: avatar4
    },
    {
      username: proxy.$t('food.ice.cream.cone'),
      province: proxy.$t('location.zhejiang'),
      sex: 1,
      age: 26,
      percentage: 70,
      pro: 0,
      color: 'rgb(var(--art-error)) !important',
      avatar: avatar5
    },
    {
      username: proxy.$t('leng.yue.dai.dai'),
      province: proxy.$t('location.hubei'),
      sex: 1,
      age: 25,
      percentage: 90,
      pro: 0,
      color: 'rgb(var(--art-success)) !important',
      avatar: avatar6
    }
  ])

  onMounted(() => {
    addAnimation()
  })

  const addAnimation = () => {
    setTimeout(() => {
      for (let i = 0; i < tableData.length; i++) {
        let item = tableData[i]
        tableData[i].pro = item.percentage
      }
    }, 100)
  }
</script>

<style lang="scss">
  .card {
    // 进度动画
    .el-progress-bar__inner {
      transition: all 1s !important;
    }

    .el-radio-button__original-radio:checked + .el-radio-button__inner {
      color: var(--el-color-primary) !important;
      background: transparent !important;
    }
  }
</style>

<style lang="scss" scoped>
  .card {
    width: 100%;
    height: 510px;
    overflow: hidden;

    .card-header {
      padding-left: 25px !important;
    }

    :deep(.el-table__body tr:last-child td) {
      border-bottom: none !important;
    }

    .avatar {
      width: 36px;
      height: 36px;
      border-radius: 6px;
    }

    .user-name {
      margin-left: 10px;
    }
  }
</style>
