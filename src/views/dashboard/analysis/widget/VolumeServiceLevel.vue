<template>
  <div class="custom-card art-custom-card volume-service-level">
    <div class="custom-card-header">
      <span class="title">{{ t('analysis.volumeServiceLevel.title') }}</span>
    </div>
    <div class="custom-card-body">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  // 服务类别数据
  const serviceCategories = ref(['产品A', '产品B', '产品C', '产品D', '产品E'])

  // 服务量数据
  const volumeServiceData = ref([
    {
      name: '业务量',
      data: [20, 25, 30, 35, 40],
      stack: 'total'
      // color: '#0095FF'
    },
    {
      name: '服务量',
      data: [30, 35, 40, 45, 50],
      stack: 'total'
      // color: '#95E0FB'
    }
  ])
</script>

<style lang="scss" scoped>
  .volume-service-level {
    height: 330px;

    .custom-card-body {
      padding: 20px;
    }
  }
</style>
