import { createApp } from 'vue'
import App from './App.vue'
import { message } from '@/utils/resetMessage'

import { initStore } from './store' // Store
import { initRouter } from './router' // Router
import '@styles/reset.scss' // 重置HTML样式
import '@styles/app.scss' // 全局样式
import '@styles/el-ui.scss' // 优化element样式
import '@styles/mobile.scss' // 移动端样式优化
import '@styles/change.scss' // 主题切换过渡优化
import '@styles/theme-animation.scss'               // 主题切换动画
import '@styles/el-light.scss'                      // Element 自定义主题（亮色）
import '@styles/el-dark.scss'                       // Element 自定义主题（暗色）
import '@styles/dark.scss'                          // 系统主题
import '@icons/system/iconfont.js'                  // 系统彩色图标
import '@icons/system/iconfont.css'                 // 系统图标
import '@utils/sys/console.ts'                      // 控制台输出内容
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { setupGlobDirectives } from './directives'
import language from './locales'
import '@styles/tailwind.css' // tailwind样式
// 权限控制现在在 router/guards 中处理
// 字典标签组件
import DictTag from '@/components/DictTag/index.vue'
import { useDict } from '@/utils/dict'
import { download, downloadQueryCase, uploadFile } from '@/utils/request'
import {
  addDateRange,
  formatStr,
  formatterVal,
  handleTree,
  isImg,
  parseTime,
  resetForm,
  selectDictLabel,
  selectDictLabels
} from '@/utils/ruoyi'

import SocketIo from '@/api/socket' // plugins
// svg图标
import 'virtual:svg-icons-register'

import plugins from './plugins'


document.addEventListener(
  'touchstart',
  function () {},
  { passive: false }
)

const app = createApp(App)

// 全局方法挂载
app.config.globalProperties.useDict = useDict
app.config.globalProperties.download = download
app.config.globalProperties.downloadQueryCase = downloadQueryCase
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.selectDictLabels = selectDictLabels
app.config.globalProperties.formatterVal = formatterVal
app.config.globalProperties.isImg = isImg
app.config.globalProperties.formatStr = formatStr
app.config.globalProperties.$msg = message // 挂载
app.config.globalProperties.$upload = uploadFile // 挂载
app.config.globalProperties.$socket = SocketIo.getInstance() // 挂载


app.component('DictTag', DictTag)

app.use(language)
initStore(app)
initRouter(app)
setupGlobDirectives(app)

app.use(plugins)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.mount('#app')

