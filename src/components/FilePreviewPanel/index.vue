<template>
  <div v-if="url && url.trim()" class="file-preview-container">
    <!-- 图片预览 -->
    <div v-if="isImageFile" class="preview-section">
      <div class="preview-label">{{ imageLabel || '图片预览' }}：</div>
      <div class="image-preview">
        <el-image
          :src="url"
          :preview-src-list="[url]"
          fit="cover"
          :style="imageStyle"
          :preview-teleported="true"
        >
          <template #error>
            <div class="image-error" :style="imageStyle">
              <el-icon><Picture /></el-icon>
              <div>图片加载失败</div>
            </div>
          </template>
        </el-image>
      </div>
    </div>

    <!-- 视频预览 -->
    <div v-else-if="isVideoFile" class="preview-section">
      <div class="preview-label">{{ videoLabel || '视频预览' }}：</div>
      <div class="video-preview">
        <video
          :src="url"
          controls
          :style="videoStyle"
          preload="metadata"
        >
          您的浏览器不支持视频播放
        </video>
      </div>
    </div>

    <!-- 音频预览 -->
    <div v-else-if="isAudioFile" class="preview-section">
      <div class="preview-label">{{ audioLabel || '音频预览' }}：</div>
      <div class="audio-preview">
        <audio
          :src="url"
          controls
          :style="audioStyle"
          preload="metadata"
        >
          您的浏览器不支持音频播放
        </audio>
      </div>
    </div>

    <!-- PDF预览 -->
    <div v-else-if="isPdfFile" class="preview-section">
      <div class="preview-label">{{ pdfLabel || 'PDF预览' }}：</div>
      <div class="pdf-preview">
        <iframe
          :src="url"
          :style="pdfStyle"
          frameborder="0"
        ></iframe>
      </div>
    </div>

    <!-- 其他文件类型 -->
    <div v-else class="preview-section">
      <div class="preview-label">{{ fileLabel || '文件信息' }}：</div>
      <div class="file-info">
        <el-card :style="fileCardStyle">
          <div class="file-info-content">
            <el-icon size="32" style="color: #409eff;"><Document /></el-icon>
            <div class="file-details">
              <div class="file-name">{{ fileName }}</div>
              <div class="file-type">文件类型: {{ fileExtension.toUpperCase() }}</div>
              <el-button 
                type="primary" 
                size="small" 
                @click="openFile" 
                style="margin-top: 8px;"
                v-if="showOpenButton"
              >
                <el-icon><View /></el-icon>
                {{ openButtonText || '打开文件' }}
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Picture, Document, View } from '@element-plus/icons-vue'

const props = defineProps({
  // 文件URL
  url: {
    type: String,
    default: ''
  },
  // 自定义标签文本
  imageLabel: String,
  videoLabel: String,
  audioLabel: String,
  pdfLabel: String,
  fileLabel: String,
  openButtonText: String,
  // 是否显示打开按钮
  showOpenButton: {
    type: Boolean,
    default: true
  },
  // 自定义样式
  imageStyle: {
    type: [String, Object],
    default: () => ({
      width: '200px',
      height: '150px',
      borderRadius: '8px'
    })
  },
  videoStyle: {
    type: [String, Object],
    default: () => ({
      width: '100%',
      maxWidth: '400px',
      height: '200px',
      borderRadius: '8px'
    })
  },
  audioStyle: {
    type: [String, Object],
    default: () => ({
      width: '100%',
      maxWidth: '400px'
    })
  },
  pdfStyle: {
    type: [String, Object],
    default: () => ({
      width: '100%',
      height: '300px',
      border: '1px solid #ddd',
      borderRadius: '8px'
    })
  },
  fileCardStyle: {
    type: [String, Object],
    default: () => ({
      maxWidth: '400px'
    })
  }
})

const emits = defineEmits(['open-file'])

// 获取文件扩展名
const fileExtension = computed(() => {
  if (!props.url) return ''
  const parts = props.url.split('.')
  return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : ''
})

// 获取文件名
const fileName = computed(() => {
  if (!props.url) return ''
  const parts = props.url.split('/')
  return parts[parts.length - 1] || 'unknown'
})

// 文件类型检测
const isImageFile = computed(() => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
  return imageExtensions.includes(fileExtension.value)
})

const isVideoFile = computed(() => {
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv']
  return videoExtensions.includes(fileExtension.value)
})

const isAudioFile = computed(() => {
  const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
  return audioExtensions.includes(fileExtension.value)
})

const isPdfFile = computed(() => {
  return fileExtension.value === 'pdf'
})

// 打开文件
const openFile = () => {
  if (props.url) {
    emits('open-file', props.url)
    window.open(props.url, '_blank')
  }
}
</script>

<style scoped>
.file-preview-container {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  background-color: var(--el-bg-color-page);
}

.preview-section {
  margin-bottom: 16px;
}

.preview-section:last-child {
  margin-bottom: 0;
}

.preview-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.image-preview {
  display: flex;
  align-items: center;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-placeholder);
  font-size: 12px;
}

.video-preview,
.audio-preview {
  display: flex;
  align-items: center;
}

.pdf-preview {
  border-radius: 8px;
  overflow: hidden;
}

.file-info-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  word-break: break-all;
}

.file-type {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

/* 暗黑主题适配 */
.dark .file-preview-container {
  background-color: var(--el-bg-color);
  border-color: var(--el-border-color);
}

.dark .image-error {
  background-color: var(--el-fill-color-darker);
}
</style>
