<template>
  <svg
    :class="svgClass"
    :style="{ height: height, width: width }"
    aria-hidden="true"
    class="svg-icon"
  >
    <use :fill="color" :xlink:href="iconName" />
  </svg>
</template>

<script>
  export default defineComponent({
    props: {
      width: {
        type: String,
        default: '1em'
      },
      height: {
        type: String,
        default: '1em'
      },
      iconClass: {
        type: String,
        required: true
      },
      className: {
        type: String,
        default: ''
      },
      color: {
        type: String,
        default: ''
      }
    },
    setup(props) {
      return {
        iconName: computed(() => `#icon-${props.iconClass}`),
        svgClass: computed(() => {
          if (props.className) {
            return `svg-icon ${props.className}`
          }
          return 'svg-icon'
        })
      }
    }
  })
</script>

<style lang="scss" scoped>
  .sub-el-icon,
  .nav-icon {
    display: inline-block;
    font-size: 15px;
    margin-right: 12px;
    position: relative;
  }

  .svg-icon {
    position: relative;
    fill: currentColor;
    //vertical-align: -2px;
  }
</style>
