<template>
  <div class="art-turnstile">
    <vue-turnstile
      ref="turnstile"
      :site-key="siteKey"
      v-model="captchaToken"
      :theme="theme"
      :size="size"
      :language="language"
      :appearance="appearance"
      :action="action"
      :cData="cData"
      :refreshExpired="refreshExpired"
      :execution="execution"
      @expired="onExpired"
      @error="onError"
      @success="onSuccess"
      @callback="onCallback"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue'
  import VueTurnstile from 'vue-turnstile'

  // 定义Turnstile组件的类型
  interface TurnstileInstance {
    reset: () => void
    execute: () => void
  }

  /**
   * Cloudflare Turnstile 验证码组件封装
   */
  const props = defineProps({
    // 站点密钥
    siteKey: {
      type: String,
      default: '0x4AAAAAABJ8-umhzD243qYK' // 默认使用示例密钥，实际使用时需替换
    },
    // 验证成功后的令牌，与v-model双向绑定
    modelValue: {
      type: String,
      default: ''
    },
    // 主题: light, dark, auto
    theme: {
      type: String,
      default: 'auto'
    },
    // 大小: normal, compact
    size: {
      type: String,
      default: 'normal'
    },
    // 语言
    language: {
      type: String,
      default: undefined
    },
    // 外观: always, execute, interaction-only
    appearance: {
      type: String,
      default: undefined
    },
    // 表单动作
    action: {
      type: String,
      default: undefined
    },
    // 自定义数据
    cData: {
      type: String,
      default: undefined
    },
    // 令牌过期后是否自动刷新
    refreshExpired: {
      type: Boolean,
      default: true
    },
    // 执行方式: execute, render, afterRender
    execution: {
      type: String,
      default: undefined
    }
  })

  const emit = defineEmits(['update:modelValue', 'expired', 'error', 'success', 'callback'])

  // 验证码组件引用
  const turnstile = ref<TurnstileInstance | null>(null)

  // 内部令牌值
  const captchaToken = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  })

  // 验证码过期
  const onExpired = () => {
    emit('expired')
  }

  // 验证码错误
  const onError = (error: any) => {
    emit('error', error)
  }

  // 验证成功
  const onSuccess = (token: string) => {
    emit('success', token)
  }

  // 验证回调
  const onCallback = (token: string) => {
    emit('callback', token)
  }

  // 主动刷新验证码
  const reset = () => {
    if (turnstile.value) {
      turnstile.value.reset()
    }
  }

  // 主动执行验证
  const execute = () => {
    if (turnstile.value) {
      turnstile.value.execute()
    }
  }

  // 暴露方法给父组件
  defineExpose({
    reset,
    execute,
    turnstile
  })
</script>

<style lang="scss" scoped>
  .art-turnstile {
    display: flex;
    justify-content: center;
    width: 100%;
    margin: 16px 0;
  }
</style>
