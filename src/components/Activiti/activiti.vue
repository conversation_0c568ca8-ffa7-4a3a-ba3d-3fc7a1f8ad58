<template>
  <div class="bpmn-viewer-container">
    <div>
      <DrawerTitle>
        <template #default>{{ $t('flow.chart') }}</template>
      </DrawerTitle>
      <div class="flex">
        <div id="bpmnCanvas"></div>
        <div class="flowMsgPopover">
          <div v-for="item in data.detailInfo" :key="item.approver">
            <p>{{ $t('approval.person') }}：{{ item.approver }}</p>
            <p>{{ $t('node.name') }}：{{ item.nodeName || '' }}</p>
            <p>{{ $t('node.status') }}：<span style="font-weight: bold">{{
              item.status || ''
                }}</span></p>
            <p>{{ $t('begin.starttime') }}：{{ item.startDate || '' }}</p>
            <p>{{ $t('end.time') }}：{{ item.endDate || '' }}</p>
            <p>{{ $t('approval.time') }}：{{ item.duration || '' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DrawerTitle>
    <template #default>{{ $t('flow.list') }}</template>
  </DrawerTitle>
  <div class="">
    <el-table :data="data.workList">
      <el-table-column :label="$t('work.sender')" prop="senderText"></el-table-column>
      <el-table-column :label="$t('work.receiver')" prop="receiverText"></el-table-column>
      <el-table-column :label="$t('node.name')" prop="nodeName"></el-table-column>
      <el-table-column :label="$t('work.status')" prop="status">
        <template #default="scope">
          <dict-tag :options="work_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('handler.time')" prop="handTime"></el-table-column>
      <el-table-column :label="$t('approval.opinion')" prop="approvalOpinion" show-overflow-tooltip></el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { getModelInfo } from '@/api/system/activitiModel/ActivitiModel'
import DrawerTitle from '@/components/DrawerTitle/DrawerTitle.vue'
import { useSettingStore } from '@/store/modules/setting'
import BpmnViewer from 'bpmn-js'
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue'

// 获取当前组件实例
const { proxy } = getCurrentInstance()
const { work_status, work_type } = proxy.useDict('work_status', 'work_type')

// props 接收
const props = defineProps({
  processId: {
    type: String,
    required: true
  }
})

let bpmnViewer = null
const activeOverlayId = ref(null)
let hoverTimer = null

// 响应式数据
const data = reactive({
  hideTimeout: {},
  workList: [],
  x: 0,
  y: 0,
  visible: true,
  detailInfo: [],
  taskInfo: {},
  highlightNode: [],
  highlightLine: [],
  runningTask: [],
  modelName: '',
  defaultZoom: 1,
  nodeDetail: {},
  scale: 1,
  nodeName: '',
  title: '流程预览'
})

// 页面初始化
function initPage() {
  bpmnViewer && bpmnViewer.destroy()
  bpmnViewer = new BpmnViewer({
    container: '#bpmnCanvas',
    additionalModules: [
      // MoveCanvasModule
    ]
  })

  const params = {
    taskId: props.processId
  }
  getModelInfo(params).then((res) => {
    if (res.code === 200) {
      const response = res.data
      importXml(response.xml)
      data.title = response.name
      data.highlightLine = response.highLightedFlowsIds
      data.highlightNode = response.highLightedTask
      data.runningTask = response.runningActivityIdList
      data.taskInfo = response.taskInfoMap
      data.workList = response.workList
      console.log(data.workList)
    } else {
      proxy.$msg({
        message: res.resultMessage,
        type: 'error'
      })
    }
  })
}

// 导入XML
async function importXml(modelXml) {
  const gatewayIds = getHtmlAttr(modelXml, 'exclusiveGateway', 'id')
  let modelXmlTemp = modelXml
  if (gatewayIds && gatewayIds.length > 0) {
    gatewayIds.forEach((item) => {
      const result = new RegExp('id="(.+?)"').exec(item)
      if (result && result[1]) {
        modelXmlTemp = modelXmlTemp.replace(
          'bpmnElement="' + result[1] + '"',
          'bpmnElement="' + result[1] + '" isMarkerVisible="true"'
        )
      }
    })
  }
  await bpmnViewer.importXML(modelXmlTemp)
  importXmlSuccess()
}

// 导入成功后处理
function importXmlSuccess() {
  const canvas = bpmnViewer.get('canvas')
  canvas.zoom('fit-viewport', 'auto')
  setViewerStyle(canvas)
  bindEvents()
}

// 设置高亮样式
function setViewerStyle(canvas) {
  const highlightNodes = data.highlightNode
  if (highlightNodes && highlightNodes.length > 0) {
    highlightNodes.forEach((node) => {
      canvas.addMarker(node, 'highlight-line')
      const ele = document.querySelector('.highlight-line').querySelector('.djs-visual rect')
      if (ele) {
        ele.setAttribute('stroke-dasharray', '4,4')
      }
    })
  }

  const runningTask = data.runningTask
  if (runningTask && runningTask.length > 0) {
    runningTask.forEach((task) => {
      canvas.addMarker(task, 'highlight')
      const ele = document.querySelector('.highlight').querySelector('.djs-visual rect')
      if (ele) {
        ele.setAttribute('stroke-dasharray', '4,4')
      }
    })
  }

  const highlightLines = data.highlightLine
  if (highlightLines && highlightLines.length > 0) {
    highlightLines.forEach((line) => {
      canvas.addMarker(line, 'highlight-line')
    })
  }
}

// 悬浮框设置
function genNodeDetailBox(element, overlaysService) {
  const taskInfo = data.taskInfo[element.id]
  if (taskInfo === undefined) {
    return
  }
  data.detailInfo = taskInfo

  // 如果已有悬浮框，不重复创建
  if (activeOverlayId.value === element.id) {
    return
  }

  // 清除之前的悬浮框
  if (activeOverlayId.value) {
    overlaysService.remove({ id: activeOverlayId.value });
  }

  activeOverlayId.value = element.id;

  nextTick(() => {
    const tempDiv = document.createElement('div')
    const popoverEl = document.querySelector('.flowMsgPopover')
    tempDiv.innerHTML = popoverEl.innerHTML
    tempDiv.className = 'tipBox'
    tempDiv.style.width = '260px'
    if (useSettingStore().isDark) {
      tempDiv.style.background = 'rgba(15, 15, 15, 1)' // 黑色背景，不透明
    } else {
      tempDiv.style.background = 'rgba(255, 255, 255, 1)' // 白色背景，不透明
    }

    tempDiv.style.overflowY = 'auto'
    tempDiv.style.position = 'absolute'
    tempDiv.style.overflowX = 'hidden'
    tempDiv.style.opacity = '0'
    tempDiv.style.transition = 'opacity 0.2s ease-in-out'

    setTimeout(() => {
      tempDiv.style.opacity = '1'
    }, 10)

    tempDiv.addEventListener('mouseenter', () => {
      if (hoverTimer) {
        clearTimeout(hoverTimer)
        hoverTimer = null
      }
    })

    tempDiv.addEventListener('mouseleave', () => {
      handleMouseLeave(overlaysService)
    })

    overlaysService.add(element.id, {
      position: { top: element.height + 10, left: 0 },
      html: tempDiv
    })
  })
}

// 处理鼠标离开事件
function handleMouseLeave(overlaysService) {
  if (hoverTimer) {
    clearTimeout(hoverTimer)
  }

  hoverTimer = setTimeout(() => {
    if (activeOverlayId.value) {
      const overlayElements = document.querySelectorAll('.tipBox')
      overlayElements.forEach(el => {
        el.style.opacity = '0'
      })

      setTimeout(() => {
        overlaysService.remove({ id: activeOverlayId.value })
        activeOverlayId.value = null
      }, 200)
    }
    hoverTimer = null
  }, 300)
}

// 为节点注册悬浮事件
function bindEvents() {
  const eventBus = bpmnViewer.get('eventBus')
  const overlaysService = bpmnViewer.get('overlays')

  eventBus.on('element.hover', (e) => {
    if (e.element.type === 'bpmn:UserTask') {
      if (hoverTimer) {
        clearTimeout(hoverTimer)
        hoverTimer = null
      }

      setTimeout(() => {
        if (e.element.type === 'bpmn:UserTask') {
          genNodeDetailBox(e.element, overlaysService)
        }
      }, 100)
    }
  })

  eventBus.on('element.click', (e) => {
    if (e.element.type !== 'bpmn:UserTask') return
    console.log(e.element)
  })

  eventBus.on('element.out', (e) => {
    if (e.element.type !== 'bpmn:UserTask') return
    handleMouseLeave(overlaysService)
  })
}

// 监听窗口变化，重新缩放
function processReZoom() {
  data.defaultZoom = 1
  bpmnViewer.get('canvas').zoom('fit-viewport', 'auto')
}

// 获取HTML属性
function getHtmlAttr(source, element, attr) {
  const result = []
  const reg = `<${element}[^<>]*?\\s${attr}=['"]?(.*?)['"]?\\s.*?>`
  const matched = source.match(new RegExp(reg, 'gi'))
  matched && matched.forEach((item) => result.push(item))
  return result
}

// onMounted 生命周期
onMounted(() => {
  initPage()
  window.onresize = () => {
    processReZoom()
  }
})
</script>

<style lang="scss">
@import 'bpmn-js/dist/assets/diagram-js.css';
@import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
@import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css';
@import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';
@import 'bpmn-js-properties-panel/dist/assets/bpmn-js-properties-panel.css';

.flowMsgPopover {
  display: none;
}

// 黑色主题下的节点样式增强
html.dark {
  .djs-element.djs-shape {
    .djs-visual {

      rect,
      circle,
      path,
      polygon {
        // stroke: #bbb !important;
        // stroke-width: 2px !important;
      }

      text {
        // fill: #fff !important;
        font-weight: 500 !important;
      }
    }
  }

  // 连线样式增强
  .djs-connection {
    .djs-visual path {
      stroke: #bbb !important;
      stroke-width: 2px !important;
    }
  }
}

.highlight:not(.djs-connection) .djs-visual> :nth-child(1) {
  fill: rgba(251, 233, 209, 1) !important;
}

.run-task g.djs-visual> :nth-child(1) {
  stroke: red !important;
  stroke-width: 2.5px !important;
}

.highlight g.djs-visual> :nth-child(1) {
  stroke: rgba(255, 126, 125, 1) !important;
  stroke-width: 2.5px !important;
}

.highlight-line g.djs-visual> :nth-child(2) {
  stroke: rgba(0, 255, 0, 1) !important;
  fill: rgba(0, 255, 0, 0.4) !important;
  stroke-width: 2.5px !important;
}

// 黑色主题下高亮效果增强
html.dark {
  .highlight:not(.djs-connection) .djs-visual> :nth-child(1) {
    fill: rgba(255, 200, 150, 0.8) !important;
    filter: drop-shadow(0 0 3px rgba(255, 200, 150, 0.5));
  }

  .highlight g.djs-visual> :nth-child(1) {
    stroke: rgba(255, 126, 125, 1) !important;
    stroke-width: 3px !important;
    filter: drop-shadow(0 0 5px rgba(255, 126, 125, 0.5));
  }

  .highlight-line g.djs-visual> :nth-child(2) {
    stroke: rgba(0, 255, 0, 1) !important;
    stroke-width: 3px !important;
    filter: drop-shadow(0 0 4px rgba(0, 255, 0, 0.5));
  }

  .djs-connection.highlight-line .djs-visual path {
    stroke: rgba(0, 255, 0, 1) !important;
    stroke-width: 2.5px !important;
  }
}

@-webkit-keyframes dynamicNode {
  to {
    stroke-dashoffset: 100%;
  }
}

.highlight {
  .djs-visual {
    -webkit-animation: dynamicNode 18s linear infinite;
    -webkit-animation-fill-mode: forwards;
  }
}

.bpmn-viewer-container {
  //height: calc(100vh - 150px) !important;
  height: 35vh;
}

.tipBox {
  width: 300px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  padding: 12px;

  p {
    line-height: 28px;
    margin: 0;
    padding: 0;
  }
}

// 黑色主题下悬浮框样式增强
html.dark {
  .tipBox {
    background: rgba(40, 40, 40, 0.95);
    border: 1px solid #555;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);

    p {
      color: #e0e0e0;
    }
  }
}

.bjs-powered-by {
  display: none;
}

.djs-container {
  overflow: visible !important;
}

.canvas {
  width: 30%;
}

.bjs-breadcrumbs {
  display: none;
}
</style>
