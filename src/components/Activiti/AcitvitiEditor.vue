<template>
  <div style="position: relative; height: 100%; width: 100%">
    <iframe
      :class="{ full: full }"
      :src="modelerUrl"
      class="iframe"
      frameborder="0"
      scrolling="auto"
      width="100%"
    />
  </div>
</template>

<script setup>
  import { getToken } from '../../utils/auth'
  import { onMounted } from 'vue'
  import { useUserStore } from '@/store/modules/user'

  let userStore = useUserStore()
  const { proxy } = getCurrentInstance()
  const props = defineProps({
    modelId: {
      required: true,
      type: String
    },
    full: {
      required: true,
      default: false
    }
  })
  let emits = defineEmits(['isUpdate'])
  const modelerUrl = computed(() => {
    return `/static/modeler.html?modelId=${props.modelId}&timestamp=${new Date().getTime()}&prefix=${import.meta.env.VITE_BASE_URL}&lang=${userStore.language}`
  })
  onMounted(() => {
    window.getMyVue = {
      token: getToken(),
      prefix: import.meta.env.VITE_BASE_URL,
      proxy: proxy,
      emits
    }
  })
</script>
<style lang="scss" scoped>
  .iframe {
    width: 100%;
    height: 80vh;
  }

  .full {
    height: calc(100vh - 56px) !important;
  }

  .propertySection.collapsed {
    max-height: 80px !important;
    height: 80px !important;
    overflow: hidden;
  }
</style>
