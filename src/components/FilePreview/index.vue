<template>
  <div class="file-preview">
    <div class="file-icon" @click="handleFileClick">
      <image-preview v-if="isImage" :height="50" :src="fileUrl" :width="50" />
      <img v-else :src="fileTypeInfo.icon" :alt="fileTypeInfo.alt" class="file-type-icon" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'

  // 接收 URL 作为 props
  interface Props {
    fileUrl: string // 传入文件的URL
  }

  const props = defineProps<Props>()

  //导入图片资源
  import excelIcon from '@/assets/img/excel-icon.png'
  import otherFileIcon from '@/assets/img/other_file.png'
  import pdfIcon from '@/assets/img/pdf-icon.png'
  import wordIcon from '@/assets/img/word-icon.png'
  import zipIcon from '@/assets/img/zip.png'

  // 文件类型配置
  interface FileTypeConfig {
    extensions: string[]
    icon: string
    alt: string
  }

  type FileTypes = {
    [key: string]: FileTypeConfig
  }

  const FILE_TYPES: FileTypes = {
    image: {
      extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      icon: '',
      alt: 'Image File'
    },
    pdf: {
      extensions: ['pdf'],
      icon: pdfIcon,
      alt: 'PDF File'
    },
    word: {
      extensions: ['doc', 'docx'],
      icon: wordIcon,
      alt: 'Word File'
    },
    excel: {
      extensions: ['xls', 'xlsx'],
      icon: excelIcon,
      alt: 'Excel File'
    },
    archive: {
      extensions: ['zip', 'rar', '7z', 'tar', 'gz'],
      icon: zipIcon,
      alt: 'Archive File'
    },
    other: {
      extensions: [],
      icon: otherFileIcon,
      alt: 'Other File'
    }
  } as const

  // 获取文件扩展名
  const getFileExtension = (url: string) => {
    return url.split('.').pop()?.toLowerCase() || ''
  }

  // 判断文件类型
  const fileType = computed(() => {
    const extension = getFileExtension(props.fileUrl)
    const foundType = Object.entries(FILE_TYPES).find(([_, type]) =>
      type.extensions.includes(extension)
    )
    return foundType ? foundType[0] : 'other'
  })

  const isImage = computed(() => fileType.value === 'image')

  const fileTypeInfo = computed(() => FILE_TYPES[fileType.value])

  // 处理文件点击
  const handleFileClick = () => {
    if (!isImage.value) {
      downloadFile()
    }
  }

  // 下载文件
  const downloadFile = () => {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileUrl.split('/').pop() || 'file'
    link.click()
  }
</script>

<style scoped>
  .file-icon {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .file-type-icon {
    width: 50px;
    height: 50px;
    object-fit: contain;
  }
</style>
