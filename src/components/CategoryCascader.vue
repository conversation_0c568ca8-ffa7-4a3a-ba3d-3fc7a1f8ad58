<template>
  <el-cascader
    v-model="selectedValue"
    :options="formattedOptions"
    :placeholder="placeholder"
    :props="cascaderProps"
    :style="{ width: width }"
    v-bind="$attrs"
    @change="handleChange"
  />
</template>

<script setup>
  import { listCategory } from '@/api/system/category/Category'
  import { computed, onMounted, ref, watch } from 'vue'

  const props = defineProps({
    // 当前选中的值
    modelValue: {
      type: [String, Number, Array],
      default: undefined
    },
    // 类型
    type: {
      type: String,
      required: true
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: '请选择分类'
    },
    // 组件宽度
    width: {
      type: String,
      default: '100%'
    },
    // 是否严格模式
    checkStrictly: {
      type: Boolean,
      default: true
    },
    // 是否返回完整路径
    emitPath: {
      type: Boolean,
      default: false
    },
    // 是否包含"全部"选项
    includeAll: {
      type: Boolean,
      default: false
    },
    // "全部"选项的文本
    allLabel: {
      type: String,
      default: '全部'
    }
  })

  const emit = defineEmits(['update:modelValue', 'change'])

  // 选中值
  const selectedValue = ref(props.modelValue)
  // 分类数据
  const categoryData = ref([])

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newVal) => {
      selectedValue.value = newVal
    }
  )

  // 监听内部值变化
  watch(selectedValue, (newVal) => {
    emit('update:modelValue', newVal)
  })

  // 获取分类数据
  const fetchCategoryData = async () => {
    try {
      const { data } = await listCategory({ type: props.type })
      if (props.includeAll) {
        categoryData.value = [
          {
            id: '0',
            categoryName: props.allLabel
          }
        ].concat(data)
      } else {
        categoryData.value = data
      }
    } catch (error) {
      console.error('获取分类数据失败:', error)
    }
  }

  // 格式化分类数据的递归函数
  const formatCategoryData = (category) => {
    const result = {
      value: category.id,
      label: category.categoryName
    }

    if (category.children && category.children.length > 0) {
      result.children = category.children.map((child) => formatCategoryData(child))
    }

    return result
  }

  // 计算级联选择器的选项数据
  const formattedOptions = computed(() => {
    return categoryData.value.map((item) => formatCategoryData(item))
  })

  // 级联选择器配置
  const cascaderProps = computed(() => ({
    value: 'value',
    label: 'label',
    children: 'children',
    checkStrictly: props.checkStrictly,
    emitPath: props.emitPath,
    expandTrigger: 'hover'
  }))

  // 值变化处理函数
  const handleChange = (value) => {
    emit('change', value)
  }

  // 组件挂载时获取数据
  onMounted(() => {
    fetchCategoryData()
  })

  // 暴露刷新方法
  defineExpose({
    refresh: fetchCategoryData
  })

  // 禁用 attribute 继承到根元素
  defineOptions({
    inheritAttrs: false
  })
</script>
