<template>
  <div class="upload-file">
    <el-upload
      ref="fileUpload"
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :headers="headers"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      class="upload-file-uploader"
      multiple
    >
      <!-- 上传按钮 -->
      <el-button type="primary">{{ $t('file.select') }}</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip"
      >{{ $t('upload.please') }}
      <template v-if="fileSize"
        >{{ $t('size.not.exceeding') }}<b style="color: #f56c6c">{{ fileSize }}MB</b></template
      >
      <template v-if="fileType"
        >{{ $t('format.is') }}<b style="color: #f56c6c">{{ fileType.join('/') }}</b></template
      >
      {{ $t('file') }}
    </div>
    <!-- 文件列表 -->
    <transition-group
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        v-for="(file, index) in fileList"
        :key="file.uid"
        class="el-upload-list__item ele-upload-list__item-content"
      >
        <el-link :href="`${baseUrl}${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" type="danger" @click="handleDelete(index)">{{
            $t('action.delete')
          }}</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup>
  import { getToken } from '@/utils/auth'
  import { getCurrentInstance } from 'vue'

  const props = defineProps({
    modelValue: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['doc', 'xls', 'ppt', 'txt', 'pdf']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    }
  })

  const { proxy } = getCurrentInstance()
  const emit = defineEmits()
  const number = ref(0)
  const uploadList = ref([])
  const baseUrl = import.meta.env.VITE_API_URL
  const uploadFileUrl = ref(import.meta.env.VITE_API_URL + '/common/upload') // 上传文件服务器地址
  const headers = ref({ Authorization: 'Bearer ' + getToken() })
  const fileList = ref([])
  const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize))

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        let temp = 1
        // 处理字符串、数组或者单个值的情况
        const list = Array.isArray(val) ? val : typeof val === 'string' ? val.split(',') : [val]
        // 转换为对象数组
        fileList.value = list
          .map((item) => {
            if (typeof item === 'string') {
              return {
                name: getFileName(item),
                url: item,
                uid: new Date().getTime() + temp++
              }
            }
            item.uid = item.uid || new Date().getTime() + temp++
            return item
          })
          .filter((item) => item.url) // 过滤掉没有url的项
      } else {
        fileList.value = []
      }
    },
    { deep: true, immediate: true }
  )

  // 上传前校检格式和大小
  function handleBeforeUpload(file) {
    // 校检文件类型
    if (props.fileType.length) {
      const fileName = file.name.split('.')
      const fileExt = fileName[fileName.length - 1]
      const isTypeOk = props.fileType.indexOf(fileExt) >= 0
      if (!isTypeOk) {
        proxy.$modal.msgError(`文件格式不确, 请上传${props.fileType.join('/')}格式文件!`)
        return false
      }
    }
    // 校检文件大小
    if (props.fileSize) {
      const isLt = file.size / 1024 / 1024 < props.fileSize
      if (!isLt) {
        proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`)
        return false
      }
    }
    proxy.$modal.loading(proxy.$t('file.uploading'))
    number.value++
    return true
  }

  // 文件个数超出
  function handleExceed() {
    proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
  }

  // 上传失败
  function handleUploadError(err) {
    proxy.$modal.msgError(proxy.$t('upload.file.fail'))
  }

  // 上传成功回调
  function handleUploadSuccess(res, file) {
    if (res.code === 200) {
      uploadList.value.push({
        name: res.fileName,
        // 修改这里，确保获取完整的URL路径
        url: res.url || res.fileName // 优先使用返回的url，如果没有则使用fileName
      })
      uploadedSuccessfully()
    } else {
      number.value--
      proxy.$modal.closeLoading()
      proxy.$modal.msgError(res.msg)
      proxy.$refs.fileUpload.handleRemove(file)
      uploadedSuccessfully()
    }
  }

  // 删除文件
  function handleDelete(index) {
    fileList.value.splice(index, 1)
    const result = listToString(fileList.value)
    emit('update:modelValue', result || '') // 如果没有文件，返回空字符串
  }

  // 上传结束处理
  function uploadedSuccessfully() {
    if (number.value > 0 && uploadList.value.length === number.value) {
      // 过滤掉没有url的文件，并合并新上传的文件
      fileList.value = fileList.value.filter((f) => f.url !== undefined).concat(uploadList.value)
      uploadList.value = []
      number.value = 0

      // 更新modelValue
      const result = listToString(fileList.value)
      emit('update:modelValue', result)
      proxy.$modal.closeLoading()
    }
  }

  // 获取文件名称
  function getFileName(name) {
    // 如果是url那么取最后的名字 如果不是直接返回
    if (name.lastIndexOf('/') > -1) {
      return name.slice(name.lastIndexOf('/') + 1)
    } else {
      return name
    }
  }

  // 对象转成指定字符串分隔
  function listToString(list, separator) {
    separator = separator || ','
    return list
      .filter((item) => item && item.url) // 确保item存在且有url
      .map((item) => item.url.trim()) // 去除可能的空格
      .filter((url) => url) // 过滤掉空值
      .join(separator)
  }
</script>

<style lang="scss" scoped>
  .upload-file-uploader {
    margin-bottom: 5px;
  }

  .upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
  }

  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }

  .ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
  }
</style>
