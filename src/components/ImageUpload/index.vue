<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUpload"
      :action="uploadImgUrl"
      :before-remove="handleDelete"
      :before-upload="handleBeforeUpload"
      :class="{ hide: fileList.length >= limit }"
      :file-list="fileList"
      :headers="headers"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-preview="handlePictureCardPreview"
      :on-success="handleUploadSuccess"
      :on-change="handleFileChange"
      :show-file-list="showFileList"
      list-type="picture-card"
      multiple
    >
      <el-icon class="avatar-uploader-icon">
        <plus />
      </el-icon>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip"
      >{{ $t('upload.please') }}
      <template v-if="fileSize"
        >{{ $t('size.not.exceeding') }}<b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType"
        >{{ $t('format.is') }}<b style="color: #f56c6c">{{ fileType.join('/') }}</b>
      </template>
      {{ $t('file') }}
    </div>

    <!-- 预览图片列表 -->
    <!-- <div v-if="localUploadedImages.length > 0" class="uploaded-images">
      <div class="image-grid">
        <div v-for="(image, index) in localUploadedImages" :key="index" class="image-item">
          <el-image :src="image" fit="cover" :preview-src-list="localUploadedImages" :initial-index="index" />
        </div>
      </div>
    </div> -->

    <el-dialog v-model="dialogVisible" :title="$t('action.preview')" append-to-body width="350px">
      <img
        :src="dialogImageUrl"
        style="display: block; height: 300px; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<script setup>
  import { getToken } from '@/utils/auth'
  import { computed, getCurrentInstance, ref, watch } from 'vue'

  const { proxy } = getCurrentInstance()

  const props = defineProps({
    modelValue: [String, Object, Array],
    showFileList: {
      type: Boolean,
      default: true
    },
    // 图片数量限制
    limit: {
      type: Number,
      default: 9999999
    },
    categoryId: {
      type: Number,
      default: null
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 50
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 上传组件的宽度
    width: {
      type: [String, Number],
      default: '100px'
    },
    // 上传组件的高度
    height: {
      type: [String, Number],
      default: '100px'
    }
  })

  const emit = defineEmits(['update:modelValue', 'change'])
  const number = ref(0)
  const uploadList = ref([])
  const dialogImageUrl = ref('')
  const dialogVisible = ref(false)
  const baseUrl = import.meta.env.VITE_BASE_URL
  const uploadImgUrl = ref(
    import.meta.env.VITE_BASE_URL + `/common/upload?categoryId=${props.categoryId}`
  ) // 上传的图片服务器地址
  const headers = ref({ Authorization: 'Bearer ' + getToken() })
  const fileList = ref([])
  const localUploadedImages = ref([])
  const form = ref({
    files: []
  })
  const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize))

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        // 确保输入是数组
        const list = Array.isArray(val)
          ? val
          : typeof val === 'string'
            ? val.split(',').filter(Boolean)
            : [val]
        // 然后将数组转为对象数组
        fileList.value = list
          .map((item) => {
            if (!item) return null
            if (typeof item === 'string') {
              if (item.indexOf(baseUrl) === -1) {
                return { name: baseUrl + item, url: item }
              } else {
                return { name: item, url: item }
              }
            }
            return item
          })
          .filter(Boolean)
      } else {
        fileList.value = []
      }
    },
    { deep: true, immediate: true }
  )

  const handleFileChange = (files) => {
    if (!files) {
      form.value.files = []
      emit('change', [])
      return
    }
    // 确保 files 是数组
    const fileArray = Array.isArray(files) ? files : [files]
    form.value.files = fileArray
    localUploadedImages.value = fileArray
      .map((file) => {
        if (!file) return null
        if (file.url) return file.url
        return URL.createObjectURL(file.raw)
      })
      .filter(Boolean)
    emit('change', fileArray)
  }

  // 上传前loading加载
  function handleBeforeUpload(file) {
    if (!file) return false
    let isImg = false
    if (props.fileType.length) {
      let fileExtension = ''
      if (file.name && file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
      }
      isImg = props.fileType.some((type) => {
        if (file.type && file.type.indexOf(type) > -1) return true
        if (fileExtension && fileExtension.indexOf(type) > -1) return true
        return false
      })
    } else {
      isImg = file.type && file.type.indexOf('image') > -1
    }
    if (!isImg) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join('/')}图片格式文件!`)
      return false
    }
    if (props.fileSize) {
      const isLt = file.size / 1024 / 1024 < props.fileSize
      if (!isLt) {
        proxy.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`)
        return false
      }
    }
    proxy.$modal.loading(proxy.$t('image.uploading'))
    number.value++
    return true
  }

  // 文件个数超出
  function handleExceed() {
    proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
  }

  // 上传成功回调
  function handleUploadSuccess(res, file) {
    if (res.code === 200) {
      uploadList.value.push({ name: res.fileName, url: res.url })
      uploadedSuccessfully()
    } else {
      number.value--
      proxy.$modal.closeLoading()
      proxy.$modal.msgError(res.msg)
      proxy.$refs.imageUpload.handleRemove(file)
      uploadedSuccessfully()
    }
  }

  // 删除图片
  function handleDelete(file) {
    const findex = Array.isArray(fileList.value)
      ? fileList.value.findIndex((f) => f.name === file.name)
      : -1
    if (findex > -1 && uploadList.value.length === number.value) {
      fileList.value.splice(findex, 1)
      emit('update:modelValue', listToString(fileList.value))
      return false
    }
  }

  // 上传结束处理
  function uploadedSuccessfully() {
    if (number.value > 0 && uploadList.value.length === number.value) {
      fileList.value = fileList.value.filter((f) => f.url !== undefined).concat(uploadList.value)
      uploadList.value = []
      number.value = 0
      emit('update:modelValue', listToString(fileList.value))
      proxy.$modal.closeLoading()
    }
  }

  // 上传失败
  function handleUploadError() {
    proxy.$modal.msgError(proxy.$t('image.upload.fail'))
    proxy.$modal.closeLoading()
  }

  // 预览
  function handlePictureCardPreview(file) {
    dialogImageUrl.value = file.url
    dialogVisible.value = true
  }

  // 对象转成指定字符串分隔
  function listToString(list, separator) {
    if (!Array.isArray(list)) return ''
    separator = separator || ','
    return list
      .filter((item) => item && item.url && !item.url.startsWith('blob:'))
      .map((item) => item.url.replace(baseUrl, ''))
    // .join(separator)
  }
</script>

<style lang="scss" scoped>
  .component-upload-image {
    :deep(.hide .el-upload--picture-card) {
      display: none;
    }

    :deep(.el-upload--picture-card) {
      width: v-bind('props.width') !important;
      height: v-bind('props.height') !important;
      line-height: v-bind('props.height') !important;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
      width: v-bind('props.width') !important;
      height: v-bind('props.height') !important;
    }
  }
</style>
