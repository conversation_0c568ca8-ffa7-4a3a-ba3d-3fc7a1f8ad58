<template>
  <p class="drawer-title big-mb-3 big-mt-3">
    <slot></slot>
  </p>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'

  const { proxy } = getCurrentInstance()
</script>

<style lang="scss" scoped>
  .drawer-title {
    font-size: 15px;
    //border-bottom: 1px solid #eee;
    padding-top: 0px;
    //padding-bottom: 10px;
    font-weight: bold;
  }

  .drawer-title::before {
    width: 6px;
    display: inline-block;
    content: '1';
    color: var(--el-color-primary);
    background-color: var(--el-color-primary);
    margin-right: 5px;
    border-radius: 5px;
  }
</style>
