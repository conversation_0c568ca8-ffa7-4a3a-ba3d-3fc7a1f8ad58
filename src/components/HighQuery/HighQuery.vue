<template>
  <div class="big-mb-3 big-rounded-b big-pl-5 big-flex">
    <div
      class="big-pb-3 big-pr-4 big-relative big-pt-5"
      style="width: 100%; border-right: 1px solid gainsboro"
    >
      <el-row>
        <el-col :span="11">
          <div class="big-flex">
            <div
              class="big-mr-2 big-inline-block big-text-right big-leading-9 big-font-bold big-items-center big-justify-center"
              style="width: 108px"
            >
              {{ $t('higth.query.one.group') }}
            </div>
            <HighQueryItem
              class="big-inline-block"
              :options="highOptions"
              :default="data.defaultList[0]"
              ref="item-0"
              @search="search"
              @update:value="(el) => setQueryValue(el, 0)"
            />
          </div>
        </el-col>
        <el-col :span="2">
          <div class="big-mr-2" v-show="data.showMoreQuery === false">
            <el-select
              class="big-w-full"
              v-model="data.queryData.allAndOr"
              clearable
              :placeholder="$t('select.please')"
            >
              <el-option
                v-for="item in data.types"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </el-col>
        <el-col :span="11">
          <div class="big-flex big-flex-shrink">
            <div
              class="big-mr-2 big-inline-block big-text-right big-leading-9 big-font-bold"
              style="width: 135px"
            >
              {{ $t('higth.query.tow.group') }}
            </div>
            <HighQueryItem
              @search="search"
              class="big-inline-block"
              :options="highOptions"
              :default="data.defaultList[1]"
              ref="item-1"
              @update:value="(el) => setQueryValue(el, 1)"
            />
          </div>
        </el-col>
      </el-row>

      <div class="query-expand-container" :class="{ 'is-expanded': data.showMore }" ref="expandContainer">
        <!--                第二-->
        <el-row v-if="data.showMoreQuery" class="big-mt-3">
          <el-col :span="11">
            <div class="big-flex">
              <div class="big-mr-2 big-inline-block big-text-right" style="width: 108px">
                <el-select
                  class="and-or-width"
                  v-model="data.queryData.queryParamsModel[2].andOr"
                  :placeholder="$t('higth.input.content')"
                >
                  <el-option
                    v-for="item in data.types"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <HighQueryItem
                @search="search"
                class="big-inline-block"
                :options="highOptions"
                :default="data.defaultList[2]"
                ref="item-2"
                @update:value="(el) => setQueryValue(el, 2)"
              />
            </div>
          </el-col>
          <el-col :span="2">
            <div class="big-mr-2">
              <el-select
                class=" "
                v-model="data.queryData.allAndOr"
                clearable
                :placeholder="$t('select.please')"
              >
                <el-option
                  v-for="item in data.types"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-col>
          <el-col :span="11">
            <div class="big-flex big-flex-shrink">
              <div class="big-mr-2 big-inline-block big-text-right" style="width: 120px">
                <el-select
                  class="and-or-width"
                  v-model="data.queryData.queryParamsModel[3].andOr"
                  :placeholder="$t('select.please')"
                >
                  <el-option
                    v-for="item in data.types"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <HighQueryItem
                @search="search"
                class="big-inline-block"
                :options="highOptions"
                :default="data.defaultList[3]"
                ref="item-3"
                @update:value="(el) => setQueryValue(el, 3)"
              />
            </div>
          </el-col>
        </el-row>
        <!--                三-->
        <el-row v-if="data.showMoreQuery" class="big-mt-3">
          <el-col :span="11">
            <div class="big-flex">
              <div class="big-mr-2 big-inline-block big-text-right" style="width: 108px">
                <el-select
                  class="and-or-width"
                  v-model="data.queryData.queryParamsModel[4].andOr"
                  :placeholder="$t('higth.input.content')"
                >
                  <el-option
                    v-for="item in data.types"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <HighQueryItem
                @search="search"
                class="big-inline-block"
                :options="highOptions"
                :default="data.defaultList[4]"
                ref="item-4"
                @update:value="(el) => setQueryValue(el, 4)"
              />
            </div>
          </el-col>
          <el-col :span="11" :offset="2">
            <div class="big-flex big-flex-shrink">
              <div class="big-mr-2 big-inline-block big-text-right" style="width: 120px">
                <el-select
                  class="and-or-width"
                  v-model="data.queryData.queryParamsModel[5].andOr"
                  :placeholder="$t('higth.input.content')"
                >
                  <el-option
                    v-for="item in data.types"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <HighQueryItem
                @search="search"
                class="big-inline-block"
                :options="highOptions"
                :default="data.defaultList[5]"
                ref="item-5"
                @update:value="(el) => setQueryValue(el, 5)"
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <div
        class="big-cursor-pointer big-absolute big-rounded toggle-case-btn"
        @click="data.showMoreCase = !data.showMoreCase"
      >
        <el-icon color="gray">
          <ArrowLeftBold v-if="!data.showMoreCase" />
          <ArrowRightBold v-else />
        </el-icon>
      </div>
      <div
        class="big-absolute big-rounded show-more-btn"
        @click="showMore"
      >
        <el-icon color="gray">
          <ArrowDownBold v-if="!data.showMore" />
          <ArrowUpBold v-else />
        </el-icon>
      </div>
      <div class="big-text-center big-mt-5" :class="data.showMoreQuery ? '' : 'mt-2'">
        <div class="big-z-50">
          <el-button type="primary" icon="Search" @click="search"
            >{{ $t('higth.search') }}
          </el-button>
          <el-button type="success" icon="Check" @click="showSaveDialog"
            >{{ $t('higth.save') }}
          </el-button>
          <el-button type="info" icon="Refresh" @click="refresh">{{ $t('higth.reset') }}</el-button>
        </div>
      </div>
    </div>
    <div
      class="relative"
      style="width: 0px; transition-property: width; transition-duration: 0.25s"
      :style="{ width: data.showMoreCase ? 200 + 'px' : 0 }"
    >
      <p
        class="big-font-bold big-text-center big-whitespace-nowrap big-ml-1"
        v-show="data.showMoreCase"
        >{{ $t('higth.save.where') }}</p
      >
      <div
        v-show="data.showMoreCase"
        style="height: 75%; width: 100%; overflow-y: scroll"
        class="big-absolute big-pl-2"
      >
        <el-tag
          :effect="tag.id === data.checkTag ? 'dark' : 'light'"
          v-for="tag in data.queryCaseList"
          :key="tag.id"
          class="big-mx-1 big-mb-1 big-pointer"
          @click="selectQueryCase(tag.id)"
          @close="removeQueryCase(tag.id)"
          closable
        >
          {{ tag.title }}
        </el-tag>
      </div>
    </div>

    <el-dialog
      v-model="dialogVisible"
      :title="$t('higth.save.case')"
      width="30%"
      :before-close="handleClose"
    >
      <el-form :model="form" :rules="rules" ref="ruleFormRef" style="max-width: 460px">
        <el-form-item :label="$t('higth.case.name')" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item :label="$t('higth.owning.form')" prop="formName">
          <el-input v-model="form.formName" disabled />
        </el-form-item>
        <el-form-item :label="$t('higth.is.default')" prop="defaultCase">
          <el-switch
            v-model="form.defaultCase"
            inline-prompt
            inactive-value="N"
            active-value="Y"
            :active-text="$t('common.yes')"
            :inactive-text="$t('common.no')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">{{ $t('higth.cancel') }}</el-button>
          <el-button type="primary" @click="submitForm(ruleFormRef)">
            {{ $t('higth.save') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { listByBusiKey } from '@/api/system/higthQueryDetail/HigthQueryDetail'
  import { addQueryCase, delQueryCase, queryMyQueryCase } from '@/api/system/queryCase'
  import { ElMessageBox } from 'element-plus'
  import { getCurrentInstance, nextTick, reactive, ref, watchEffect } from 'vue'
  import HighQueryItem from '../HighQueryItem/HighQueryItem.vue'
  import mittBus from '@/utils/sys/mittBus'

  const { proxy } = getCurrentInstance()

  const props = defineProps({
    formKey: { type: String, default: '' },
    formName: { type: String, default: '' },
    defaultVal: {
      type: Object,
      default: {}
    },
    options: {
      type: Array,
      default: () => []
    }
  })
  const highOptions = ref(props.options)

  function setQueryValue(el, idx) {
    try {
      data.queryData.queryParamsModel[idx] = { ...data.queryData.queryParamsModel[idx], ...el }
    } catch (e) {}
  }

  const ruleFormRef = ref<FormInstance>()

  const rules = reactive({
    title: [{ required: true, message: proxy.$t('higth.query.case.name'), trigger: 'blur' }],
    formKey: [{ required: true, message: proxy.$t('higth.query.owning.form'), trigger: 'blur' }],
    defaultCase: [{ required: true, message: proxy.$t('higth.query.default.not'), trigger: 'blur' }]
  })

  const emits = defineEmits(['search', 'refresh', 'load', 'showMore'])

  const data = reactive({
    checkTag: '',
    defaultList: highOptions.value.filter((el) => el.isDefault === 'Y' || el.default),
    queryCaseList: [],
    queryData: {
      allAndOr: 'and',
      queryType: 'all',
      queryParamsModel: [
        {},
        {},
        { andOr: 'and' },
        { andOr: 'and' },
        { andOr: 'and' },
        { andOr: 'and' }
      ]
    },
    types: [
      {
        label: proxy.$t('higth.query.and'),
        value: 'and'
      },
      {
        label: proxy.$t('higth.query.or'),
        value: 'or'
      }
    ],
    showMoreCase: false,
    showMoreQuery: false,
    showMore: false
  })

  watchEffect(() => {
    data.defaultList = highOptions.value.filter((el) => el.isDefault === 'Y' || el.default)
  })

  let dialogVisible = ref(false)

  const cancel = () => {
    form.title = ''
    dialogVisible.value = false
    form.defaultCase = 'N'
  }

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        form.content = JSON.stringify(data.queryData)
        addQueryCase(form).then((response) => {
          proxy.$modal.msgSuccess(proxy.$t('higth.add.success'))
          dialogVisible.value = false
          init()
          form.title = ''
          form.defaultCase = 'N'
        })
      }
    })
  }

  function showSaveDialog() {
    dialogVisible.value = true
  }

  const handleClose = (done: () => void) => {
    ElMessageBox.confirm(proxy.$t('higth.confirm.close'))
      .then(() => {
        done()
      })
      .catch(() => {})
  }

  async function refresh() {
    // data.defaultList = highOptions.value.filter((el) => el.default)
    highOptions.value.forEach((el) => (el.value = ''))
    await Object.keys(proxy.$refs).forEach((el) => {
      if (el.startsWith('item')) {
        proxy.$refs[el]?.clear()
      }
    })
    data.checkTag = ''
    emits('refresh', getQuerData())
  }

  function search() {
    emits('search', getQuerData())
  }

  function getQuerData() {
    return data.queryData
  }

  // 防抖处理，避免快速点击导致的状态混乱
  let isAnimating = false
  const expandContainer = ref(null)

  function showMore() {
    if (isAnimating) return
    isAnimating = true

    if (data.showMore) {
      // 收起时：同步开始动画
      data.showMore = false
      mittBus.emit('toggleTableHeight')

      setTimeout(() => {
        data.showMoreQuery = false
        isAnimating = false
      }, 300) // 等待动画完成
    } else {
      // 展开时：先显示内容，测量实际高度，然后开始动画
      data.showMoreQuery = true

      nextTick(() => {
        // 获取实际内容高度
        if (expandContainer.value) {
          const actualHeight = expandContainer.value.scrollHeight
          expandContainer.value.style.setProperty('--actual-height', `${actualHeight}px`)
        }

        // 同步触发动画
        data.showMore = true
        mittBus.emit('toggleTableHeight')

        setTimeout(() => {
          isAnimating = false
        }, 300)
      })
    }
    emits('showMore', data.showMore)
  }

  function removeQueryCase(id) {
    proxy.$modal.confirm(proxy.$t('higth.remove.confirm')).then(() => {
      delQueryCase(id).then((res) => {
        proxy.$modal.msgSuccess(proxy.$t('higth.remove.success'))
        init()
      })
    })
  }

  const selectQueryCase = (id: string) => {
    if (id === data.checkTag) {
      data.checkTag = ''
      refresh()
      return
    }
    let element = data.queryCaseList.find((el) => el.id === id)
    data.checkTag = id
    let parse = JSON.parse(element.content)
    data.defaultList = parse.queryParamsModel
    emits('load', getQuerData())
  }

  async function init() {
    await queryMyQueryCase({ formKey: props.formKey }).then((res) => {
      let arr = res.data.filter((el) => el.defaultCase === 'Y')
      if (arr.length > 0) {
        let defaultCase = JSON.parse(arr[0].content)
        data.defaultList = defaultCase.queryParamsModel
      }
      data.queryCaseList = res.data

      let filter = res.data.filter((el) => el.defaultCase === 'Y')
      if (filter.length > 0) {
        data.checkTag = filter[0].id
      }
    })
  }

  proxy.$nextTick(async () => {
    await init()
    await getQueryItems()
    await emits('load', getQuerData())
  })

  const getQueryItems = async () => {
    if (highOptions.value.length === 0) {
      await listByBusiKey(toCamelCase(props.formKey)).then(({ data }) => {
        if (Object.keys(props.defaultVal).length > 0) {
          const map = data.reduce((acc, obj) => {
            acc.set(obj.column, obj)
            return acc
          }, new Map())
          Object.keys(props.defaultVal).forEach((el) => {
            let option = map.get(el)
            let defaultVal = props.defaultVal[el]
            option.value = defaultVal.value
            option.condition = defaultVal.condition
          })
        }
        highOptions.value = data
      })
    }
  }

  function toCamelCase(underscoreName) {
    return underscoreName
      .replace('t_', '')
      .split('_')
      .map((word, index) => {
        // 对于第一个单词，保持原样；对于后续单词，首字母大写
        return index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
      })
      .join('')
  }

  const form = reactive({
    title: '',
    formKey: props.formKey,
    formName: props.formName,
    defaultCase: 'N',
    content: ''
  })
</script>

<style scoped>
  .key-width {
    width: 70px;
  }

  .condition-width {
    width: 100px;
  }

  .and-or-width {
    width: 90px;
  }

  .val-width {
    width: 200px;
  }

  .query-expand-container {
    --actual-height: 300px;
    height: 0;
    opacity: 0;
    overflow: hidden;
    transform-origin: top;
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: height, opacity;
    /* 创建新的渲染层，避免影响其他元素 */
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  .query-expand-container.is-expanded {
    height: var(--actual-height);
    opacity: 1;
  }

  /* 优化右侧展开收缩面板的过渡效果 */
  .relative {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  }

  /* 添加 will-change 提示浏览器优化渲染 */
  .big-absolute {
    will-change: transform, opacity;
  }


  /* 右侧面板切换按钮样式优化 */
  .toggle-case-btn {
    top: 40%;
    right: 0px;
    padding: 4px;
    z-index: 100;

    /* 增大点击区域，但保持视觉简洁 */
    min-width: 24px;
    min-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      .el-icon {
        color: var(--el-color-primary) !important;
        transform: scale(1.1);
        transition: all 0.2s ease;
      }
    }
  }

  /* 展开/收起按钮样式优化 */
  .show-more-btn {
    bottom: 0;
    right: 30px;
    padding: 4px;
    cursor: pointer;
    z-index: 100; /* 确保在按钮区域之上 */

    /* 增大点击区域，但保持视觉简洁 */
    min-width: 24px;
    min-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      .el-icon {
        color: var(--el-color-primary) !important;
        transform: scale(1.1);
        transition: all 0.2s ease;
      }
    }
  }

  /* 优化按钮区域的布局稳定性 */
  .big-text-center {
    /* 防止按钮区域在动画时发生位移 */
    position: relative;
    z-index: 1;
  }
</style>
