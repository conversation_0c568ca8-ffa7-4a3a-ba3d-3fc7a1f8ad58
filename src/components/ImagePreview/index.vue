<template>
  <el-image
    :preview-src-list="realSrcList"
    :src="realSrc"
    :style="imageStyle"
    fit="cover"
    preview-teleported
    loading="lazy"
    :initial-index="0"
  >
    <template #error>
      <div class="image-slot">
        <el-icon>
          <picture-filled />
        </el-icon>
      </div>
    </template>
    <template #placeholder>
      <div class="image-slot">
        <el-icon class="is-loading">
          <loading />
        </el-icon>
      </div>
    </template>
  </el-image>
</template>

<script setup>
  import { isExternal } from '@/utils/validate'
  import { computed } from 'vue'

  const props = defineProps({
    src: {
      type: String,
      default: ''
    },
    width: {
      type: [Number, String],
      default: ''
    },
    height: {
      type: [Number, String],
      default: ''
    }
  })

  // 优化：将样式计算合并为一个computed
  const imageStyle = computed(() => {
    const width = typeof props.width === 'string' ? props.width : `${props.width}px`
    const height = typeof props.height === 'string' ? props.height : `${props.height}px`
    return `width:${width};height:${height};`
  })

  // 优化：使用 useMemo 缓存结果，避免重复计算
  const realSrc = computed(() => {
    if (!props.src) return ''
    const real_src = props.src.split(',')[0]
    return isExternal(real_src) ? real_src : import.meta.env.VITE_API_URL + real_src
  })

  // 优化：减少不必要的数组操作
  const realSrcList = computed(() => {
    if (!props.src) return []
    return props.src
      .split(',')
      .map((item) => (isExternal(item) ? item : import.meta.env.VITE_API_URL + item))
  })
</script>

<style lang="scss" scoped>
  .el-image {
    border-radius: 5px;
    background-color: #ebeef5;
    box-shadow: 0 0 5px 1px #ccc;

    :deep(.el-image__inner) {
      transition: transform 0.3s;
      cursor: pointer;
      will-change: transform;

      &:hover {
        transform: scale(1.2);
      }
    }

    :deep(.image-slot) {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      color: #909399;
      font-size: 30px;

      .is-loading {
        animation: rotating 2s linear infinite;
      }
    }
  }

  @keyframes rotating {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
</style>
