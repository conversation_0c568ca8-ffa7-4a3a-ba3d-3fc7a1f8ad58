<!-- 统计卡片 -->
<template>
  <div
    ref="containerRef"
    class="highlight-group"
    @mousemove="onMouseMove"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <div
      :style="{ backgroundColor: backgroundColor }"
      class="highlight-item stats-card art-custom-card"
      :class="{ 'is-hovered': isHovered }"
    >
      <!-- 跟随鼠标的光晕效果 -->
      <div
        :style="{
          '--mouse-x': mouseX + 'px',
          '--mouse-y': mouseY + 'px'
        }"
        class="glow-overlay"
      ></div>

      <!-- 粒子效果画布 -->
      <canvas
        v-if="enableParticles"
        ref="canvasRef"
        class="particles-canvas"
        :class="{ 'particles-visible': isHovered }"
      ></canvas>

      <!-- 原有内容 -->
      <div class="stats-card__content-wrapper">
        <div
          v-if="icon"
          :style="{ backgroundColor: iconBgColor, borderRadius: iconBgRadius + 'px' }"
          class="stats-card__icon"
        >
          <i
            :style="{
              color: iconColor,
              fontSize: iconSize + 'px'
            }"
            class="iconfont-sys"
            v-html="icon"
          ></i>
        </div>
        <div class="stats-card__content">
          <p v-if="title" :style="{ color: textColor }" class="stats-card__title">
            {{ title }}
          </p>
          <ArtCountTo
            v-if="count"
            :duration="2000"
            :target="count"
            class="stats-card__count"
            separator=","
          />
          <p v-if="description" :style="{ color: textColor }" class="stats-card__description">{{
            description
          }}</p>
        </div>
        <div v-if="showArrow" class="stats-card__arrow">
          <i class="iconfont-sys">&#xe703;</i>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { nextTick, onMounted, onUnmounted, ref } from 'vue'

  defineOptions({ name: 'ArtStatsCard' })

  interface StatsCardProps {
    /** 图标 */
    icon?: string
    /** 标题 */
    title?: string
    /** 数值 */
    count?: number
    /** 描述 */
    description: string
    /** 图标颜色 */
    iconColor?: string
    /** 图标背景颜色 */
    iconBgColor?: string
    /** 图标圆角大小 */
    iconBgRadius?: number
    /** 图标大小 */
    iconSize?: number
    /** 文本颜色 */
    textColor?: string
    /** 背景颜色 */
    backgroundColor?: string
    /** 是否显示箭头 */
    showArrow?: boolean
    /** 是否启用粒子效果 */
    enableParticles?: boolean
    /** 粒子数量 */
    particleQuantity?: number
    /** 粒子颜色 */
    particleColor?: string
    /** 高亮颜色 */
    highlightColor?: string
    /** 粒子颜色变化强度 */
    colorIntensity?: number
  }

  const props = withDefaults(defineProps<StatsCardProps>(), {
    iconSize: 30,
    iconBgRadius: 50,
    enableParticles: false,
    particleQuantity: 50,
    particleColor: '#ffffff',
    highlightColor: '#10b981',
    colorIntensity: 0.8
  })

  // 响应式引用
  const containerRef = ref<HTMLDivElement>()
  const canvasRef = ref<HTMLCanvasElement>()
  const mouseX = ref(0)
  const mouseY = ref(0)
  const isHovered = ref(false)

  // 粒子系统相关
  let animationId: number | null = null
  let context: CanvasRenderingContext2D | null = null
  let particles: Particle[] = []
  let canvasSize = { w: 0, h: 0 }
  let mouse = { x: 0, y: 0 }

  interface Particle {
    x: number
    y: number
    translateX: number
    translateY: number
    size: number
    alpha: number
    targetAlpha: number
    dx: number
    dy: number
    magnetism: number
    hue: number
    saturation: number
    lightness: number
  }

  // 鼠标移动处理
  const onMouseMove = (event: MouseEvent) => {
    if (!containerRef.value) return

    const rect = containerRef.value.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // 更新鼠标位置（用于光晕效果）
    mouseX.value = x
    mouseY.value = y

    // 更新粒子系统的鼠标位置
    if (props.enableParticles && canvasRef.value && isHovered.value) {
      mouse.x = x
      mouse.y = y
    }
  }

  // 鼠标进入
  const onMouseEnter = () => {
    isHovered.value = true
    if (props.enableParticles && !animationId) {
      animate()
    }
  }

  // 鼠标离开
  const onMouseLeave = () => {
    isHovered.value = false
    mouseX.value = 0
    mouseY.value = 0
    mouse.x = 0
    mouse.y = 0

    // 停止粒子动画
    if (animationId) {
      cancelAnimationFrame(animationId)
      animationId = null
    }

    // 清空画布
    if (context) {
      context.clearRect(0, 0, canvasSize.w, canvasSize.h)
    }
  }

  // 十六进制颜色转RGB
  const hexToRgb = (hex: string): number[] => {
    hex = hex.replace('#', '')
    const hexInt = parseInt(hex, 16)
    const red = (hexInt >> 16) & 255
    const green = (hexInt >> 8) & 255
    const blue = hexInt & 255
    return [red, green, blue]
  }

  // RGB转HSL
  const rgbToHsl = (r: number, g: number, b: number): number[] => {
    r /= 255
    g /= 255
    b /= 255

    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h = 0, s = 0, l = (max + min) / 2

    if (max !== min) {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break
        case g: h = (b - r) / d + 2; break
        case b: h = (r - g) / d + 4; break
      }
      h /= 6
    }

    return [h * 360, s * 100, l * 100]
  }

  // HSL转RGB
  const hslToRgb = (h: number, s: number, l: number): number[] => {
    h /= 360
    s /= 100
    l /= 100

    const hue2rgb = (p: number, q: number, t: number): number => {
      if (t < 0) t += 1
      if (t > 1) t -= 1
      if (t < 1/6) return p + (q - p) * 6 * t
      if (t < 1/2) return q
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
      return p
    }

    let r, g, b
    if (s === 0) {
      r = g = b = l
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s
      const p = 2 * l - q
      r = hue2rgb(p, q, h + 1/3)
      g = hue2rgb(p, q, h)
      b = hue2rgb(p, q, h - 1/3)
    }

    return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)]
  }

  // 初始化画布
  const initCanvas = () => {
    if (!canvasRef.value || !containerRef.value) return

    context = canvasRef.value.getContext('2d')
    if (!context) return

    const dpr = window.devicePixelRatio || 1
    canvasSize.w = containerRef.value.offsetWidth
    canvasSize.h = containerRef.value.offsetHeight

    canvasRef.value.width = canvasSize.w * dpr
    canvasRef.value.height = canvasSize.h * dpr
    canvasRef.value.style.width = `${canvasSize.w}px`
    canvasRef.value.style.height = `${canvasSize.h}px`
    context.scale(dpr, dpr)

    createParticles()
  }

  // 创建粒子
  const createParticles = () => {
    particles = []
    for (let i = 0; i < props.particleQuantity; i++) {
      particles.push(createParticle())
    }
  }

  // 创建单个粒子
  const createParticle = (): Particle => {
    const baseColor = hexToRgb(props.particleColor)
    const hsl = rgbToHsl(baseColor[0], baseColor[1], baseColor[2])

    return {
      x: Math.random() * canvasSize.w,
      y: Math.random() * canvasSize.h,
      translateX: 0,
      translateY: 0,
      size: Math.random() * 2.5 + 0.8,
      alpha: 0,
      targetAlpha: Math.random() * 0.3 + 0.15,
      dx: (Math.random() - 0.5) * 0.4,
      dy: (Math.random() - 0.5) * 0.4,
      magnetism: 0.2 + Math.random() * 3, // 降低磁性强度
      hue: hsl[0] + (Math.random() - 0.5) * 60, // 色相变化范围 ±30度
      saturation: Math.max(0, Math.min(100, hsl[1] + (Math.random() - 0.5) * 40)), // 饱和度变化
      lightness: Math.max(20, Math.min(80, hsl[2] + (Math.random() - 0.5) * 30)) // 亮度变化
    }
  }

  // 绘制粒子
  const drawParticle = (particle: Particle) => {
    if (!context) return

    const { x, y, translateX, translateY, size, alpha } = particle

    // 计算粒子相对于鼠标的距离 - 修正坐标计算
    const particleX = x + translateX
    const particleY = y + translateY
    const mouseDistance = Math.sqrt(
      Math.pow(particleX - mouse.x, 2) +
      Math.pow(particleY - mouse.y, 2)
    )

    // 根据距离调整颜色 - 更自然的颜色变化
    const colorInfluenceRadius = 100
    const colorDistanceRatio = Math.min(mouseDistance / colorInfluenceRadius, 1)

    // 动态调整色相，距离鼠标越近颜色越鲜艳，但变化更温和
    const colorIntensity = (1 - colorDistanceRatio) * props.colorIntensity
    const dynamicHue = particle.hue + colorIntensity * 40
    const dynamicSaturation = particle.saturation + colorIntensity * 25
    const dynamicLightness = particle.lightness + colorIntensity * 15

    const rgb = hslToRgb(
      dynamicHue % 360,
      Math.min(100, dynamicSaturation),
      Math.min(80, dynamicLightness)
    )

    // 根据距离调整粒子大小 - 更微妙的变化
    const dynamicSize = size * (1 + colorIntensity * 0.3)

    context.save()
    context.translate(translateX, translateY)
    context.beginPath()
    context.arc(x, y, dynamicSize, 0, 2 * Math.PI)

    // 创建径向渐变效果
    const gradient = context.createRadialGradient(x, y, 0, x, y, dynamicSize)
    gradient.addColorStop(0, `rgba(${rgb.join(', ')}, ${alpha})`)
    gradient.addColorStop(0.7, `rgba(${rgb.join(', ')}, ${alpha * 0.5})`)
    gradient.addColorStop(1, `rgba(${rgb.join(', ')}, 0)`)

    context.fillStyle = gradient
    context.fill()
    context.restore()
  }

  // 动画循环
  const animate = () => {
    if (!context || !isHovered.value) return

    context.clearRect(0, 0, canvasSize.w, canvasSize.h)

    particles.forEach((particle, i) => {
      // 更新粒子位置
      particle.x += particle.dx
      particle.y += particle.dy

      // 计算到鼠标的距离 - 修正坐标计算
      const particleX = particle.x + particle.translateX
      const particleY = particle.y + particle.translateY
      const mouseDistance = Math.sqrt(
        Math.pow(particleX - mouse.x, 2) +
        Math.pow(particleY - mouse.y, 2)
      )

      // 优化的粒子行为 - 不是所有粒子都往鼠标聚集
      const influenceRadius = 120 // 影响半径
      const repelRadius = 30 // 排斥半径

      if (mouseDistance < influenceRadius) {
        const ease = 25 + mouseDistance * 0.08
        const influence = (influenceRadius - mouseDistance) / influenceRadius

        if (mouseDistance < repelRadius) {
          // 太近时产生排斥力，避免粒子都聚集在鼠标位置
          const repelForce = (repelRadius - mouseDistance) / repelRadius * 0.5
          const repelX = (particleX - mouse.x) * repelForce
          const repelY = (particleY - mouse.y) * repelForce

          particle.translateX += repelX / ease
          particle.translateY += repelY / ease
        } else {
          // 适中距离时产生轻微吸引力
          const attractionForce = influence * particle.magnetism * 0.3
          const attractionX = (mouse.x - particleX) * attractionForce / 100
          const attractionY = (mouse.y - particleY) * attractionForce / 100

          particle.translateX += attractionX / ease
          particle.translateY += attractionY / ease
        }
      }

      // 添加轻微的随机漂移，让粒子更自然
      particle.translateX += (Math.random() - 0.5) * 0.1
      particle.translateY += (Math.random() - 0.5) * 0.1

      // 边界检测和透明度处理
      const edge = [
        particle.x + particle.translateX - particle.size,
        canvasSize.w - particle.x - particle.translateX - particle.size,
        particle.y + particle.translateY - particle.size,
        canvasSize.h - particle.y - particle.translateY - particle.size
      ]
      const closestEdge = Math.min(...edge)
      const remapClosestEdge = Math.max(0, Math.min(1, closestEdge / 30))

      // 动态透明度 - 距离鼠标越近越亮，但不要过于明显
      const mouseProximity = Math.max(0, 1 - mouseDistance / 120)
      const dynamicTargetAlpha = particle.targetAlpha * (1 + mouseProximity * 1.2)

      if (remapClosestEdge > 1) {
        particle.alpha += 0.03
        if (particle.alpha > dynamicTargetAlpha) {
          particle.alpha = dynamicTargetAlpha
        }
      } else {
        particle.alpha = dynamicTargetAlpha * remapClosestEdge
      }

      // 重置越界粒子
      if (
        particle.x < -particle.size * 2 ||
        particle.x > canvasSize.w + particle.size * 2 ||
        particle.y < -particle.size * 2 ||
        particle.y > canvasSize.h + particle.size * 2
      ) {
        particles[i] = createParticle()
      } else {
        drawParticle(particle)
      }
    })

    if (isHovered.value) {
      animationId = requestAnimationFrame(animate)
    }
  }

  // 组件挂载
  onMounted(() => {
    nextTick(() => {
      if (props.enableParticles) {
        initCanvas()
        // 不自动启动动画，只在鼠标悬浮时启动
      }
    })

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  })

  // 组件卸载
  onUnmounted(() => {
    if (animationId) {
      cancelAnimationFrame(animationId)
    }
    window.removeEventListener('resize', handleResize)
  })

  // 处理窗口大小变化
  const handleResize = () => {
    if (props.enableParticles) {
      initCanvas()
    }
  }
</script>

<style lang="scss" scoped>
  .highlight-group {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .highlight-item {
    position: relative;
    overflow: visible; // 允许光晕溢出边界
    padding: 1px;

    // 移除固定光晕，改为跟随鼠标的光晕

    // 边框高亮效果
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      z-index: 10;
      border-radius: calc(var(--custom-radius) + 4px);
      border: 1px solid transparent;
      background: linear-gradient(135deg,
        rgba(255,255,255,0.08) 0%,
        rgba(255,255,255,0.03) 50%,
        rgba(255,255,255,0.08) 100%
      ) border-box;
      mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &.is-hovered::before {
      opacity: 1;
    }

    &.is-hovered::after {
      opacity: 1;
    }
  }

  .stats-card {
    position: relative;
    display: flex;
    align-items: center;
    height: 8rem;
    padding: 0 20px;
    cursor: pointer;
    background-color: var(--art-main-bg-color);
    border-radius: calc(var(--custom-radius) + 4px) !important;
    transition: transform 0.2s ease;
    z-index: 20;
    overflow: hidden; // 卡片内容仍然裁剪

    &:hover {
      transform: translateY(-2px);
    }
  }





  .glow-overlay {
    position: absolute;
    inset: -100px;
    pointer-events: none;
    z-index: 5;

    // 创建多个不规则光晕层
    &::before {
      content: '';
      position: absolute;
      width: 180px;
      height: 160px; // 不规则椭圆
      left: calc(var(--mouse-x) - 90px + 100px);
      top: calc(var(--mouse-y) - 80px + 100px);
      background: radial-gradient(
        ellipse 70% 85% at 40% 30%,
        color-mix(in srgb, v-bind(highlightColor) 12%, transparent) 0%,
        color-mix(in srgb, v-bind(highlightColor) 8%, transparent) 30%,
        color-mix(in srgb, v-bind(highlightColor) 4%, transparent) 60%,
        transparent 100%
      );
      opacity: 0;
      transition: opacity 0.4s ease;
      filter: blur(15px);
      border-radius: 60% 40% 70% 30%; // 不规则形状
      transform: rotate(25deg); // 随机旋转
    }

    &::after {
      content: '';
      position: absolute;
      width: 140px;
      height: 200px; // 另一个不规则椭圆
      left: calc(var(--mouse-x) - 70px + 100px);
      top: calc(var(--mouse-y) - 100px + 100px);
      background: radial-gradient(
        ellipse 85% 60% at 60% 70%,
        color-mix(in srgb, v-bind(highlightColor) 8%, transparent) 0%,
        color-mix(in srgb, v-bind(highlightColor) 5%, transparent) 40%,
        color-mix(in srgb, v-bind(highlightColor) 2%, transparent) 70%,
        transparent 100%
      );
      opacity: 0;
      transition: opacity 0.5s ease;
      filter: blur(20px);
      border-radius: 40% 60% 30% 70%; // 不同的不规则形状
      transform: rotate(-15deg); // 反向旋转
    }
  }

  .highlight-item.is-hovered .glow-overlay::before,
  .highlight-item.is-hovered .glow-overlay::after {
    opacity: 1;
  }

  .particles-canvas {
    position: absolute;
    inset: 0;
    pointer-events: none;
    z-index: 15;
    border-radius: calc(var(--custom-radius) + 4px);
    opacity: 0;
    transition: opacity 0.3s ease;

    &.particles-visible {
      opacity: 1;
    }
  }

  .stats-card__content-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    z-index: 25;
  }

  .stats-card__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 46px;
    height: 46px;
    margin-right: 16px;
    border-radius: 50%;
    position: relative;
    z-index: 30;

    i {
      font-size: 30px;
    }
  }

  .stats-card__content {
    flex: 1;
    position: relative;
    z-index: 30;
  }

  .stats-card__title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: var(--art-gray-900);
  }

  .stats-card__count {
    margin: 0;
    font-size: 28px;
    font-weight: 500;
    color: var(--art-gray-900);
  }

  .stats-card__description {
    margin: 4px 0 0;
    font-size: 14px;
    color: var(--art-gray-600);
    opacity: 0.9;
  }

  .stats-card__arrow {
    position: relative;
    z-index: 30;

    i {
      font-size: 18px;
      color: var(--art-gray-600);
    }
  }
</style>
