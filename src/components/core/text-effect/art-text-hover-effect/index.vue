<template>
  <svg
    ref="svgRef"
    width="100%"
    height="100%"
    :viewBox="viewBox"
    xmlns="http://www.w3.org/2000/svg"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @mousemove="handleMouseMove"
    class="select-none cursor-pointer"
  >
    <defs>
      <!-- 彩色渐变 -->
      <linearGradient
        id="textGradient"
        gradientUnits="userSpaceOnUse"
        cx="50%"
        cy="50%"
        r="25%"
      >
        <template v-if="hovered">
          <stop offset="0%" :stop-color="colors[0]" />
          <stop offset="25%" :stop-color="colors[1]" />
          <stop offset="50%" :stop-color="colors[2]" />
          <stop offset="75%" :stop-color="colors[3]" />
          <stop offset="100%" :stop-color="colors[4]" />
        </template>
      </linearGradient>

      <!-- 径向渐变遮罩 -->
      <radialGradient
        id="revealMask"
        gradientUnits="userSpaceOnUse"
        r="20%"
        :cx="maskPosition.cx"
        :cy="maskPosition.cy"
        class="transition-all duration-0 ease-out"
      >
        <stop offset="0%" stop-color="white" />
        <stop offset="100%" stop-color="black" />
      </radialGradient>
      
      <mask id="textMask">
        <rect
          x="0"
          y="0"
          width="100%"
          height="100%"
          fill="url(#revealMask)"
        />
      </mask>
    </defs>

    <!-- 背景描边文本 -->
    <text
      x="50%"
      y="50%"
      text-anchor="middle"
      dominant-baseline="middle"
      stroke-width="0.3"
      :class="textClasses"
      :style="{ opacity: hovered ? 0.7 : 0 }"
    >
      {{ text }}
    </text>

    <!-- 动画描边文本 -->
    <text
      x="50%"
      y="50%"
      text-anchor="middle"
      dominant-baseline="middle"
      stroke-width="0.3"
      :class="strokeTextClasses"
      :style="strokeDashStyle"
    >
      {{ text }}
    </text>

    <!-- 高亮文本 -->
    <text
      x="50%"
      y="50%"
      text-anchor="middle"
      dominant-baseline="middle"
      stroke="url(#textGradient)"
      stroke-width="0.3"
      mask="url(#textMask)"
      :class="highlightTextClasses"
    >
      {{ text }}
    </text>
  </svg>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

interface Props {
  text?: string
  duration?: number
  colors?: string[]
  viewBox?: string
  fontSize?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: 'HOVER',
  duration: 0,
  colors: () => ['#eab308', '#ef4444', '#3b82f6', '#06b6d4', '#8b5cf6'],
  viewBox: '0 0 300 100',
  fontSize: 'text-7xl'
})

const svgRef = ref<SVGSVGElement>()
const cursor = ref({ x: 0, y: 0 })
const hovered = ref(false)
const maskPosition = ref({ cx: '50%', cy: '50%' })
const strokeDashOffset = ref(1000)
const strokeDashArray = ref(1000)

// 计算样式类
const textClasses = computed(() => [
  'font-bold',
  'stroke-neutral-200',
  'dark:stroke-neutral-800',
  'fill-transparent',
  props.fontSize,
  'transition-opacity',
  'duration-300'
])

const strokeTextClasses = computed(() => [
  'font-bold',
  'fill-transparent',
  'stroke-neutral-200',
  'dark:stroke-neutral-800',
  props.fontSize
])

const highlightTextClasses = computed(() => [
  'font-bold',
  'fill-transparent',
  props.fontSize
])

// 描边动画样式
const strokeDashStyle = computed(() => ({
  strokeDashoffset: strokeDashOffset.value,
  strokeDasharray: strokeDashArray.value,
  transition: 'stroke-dashoffset 4s ease-in-out'
}))

// 鼠标事件处理
const handleMouseEnter = () => {
  hovered.value = true
}

const handleMouseLeave = () => {
  hovered.value = false
  cursor.value = { x: 0, y: 0 }
  maskPosition.value = { cx: '50%', cy: '50%' }
}

const handleMouseMove = (e: MouseEvent) => {
  cursor.value = { x: e.clientX, y: e.clientY }
}

// 监听鼠标位置变化，更新遮罩位置
watch(cursor, (newCursor) => {
  if (svgRef.value && newCursor.x !== 0 && newCursor.y !== 0) {
    const svgRect = svgRef.value.getBoundingClientRect()
    const cxPercentage = ((newCursor.x - svgRect.left) / svgRect.width) * 100
    const cyPercentage = ((newCursor.y - svgRect.top) / svgRect.height) * 100
    
    maskPosition.value = {
      cx: `${Math.max(0, Math.min(100, cxPercentage))}%`,
      cy: `${Math.max(0, Math.min(100, cyPercentage))}%`
    }
  }
}, { deep: true })

// 组件挂载后启动描边动画
onMounted(() => {
  setTimeout(() => {
    strokeDashOffset.value = 0
  }, 100)
})
</script>

<style scoped>
/* 确保SVG文本渲染正确 */
text {
  font-family: 'Helvetica', sans-serif;
}

/* 平滑的遮罩过渡 */
#revealMask {
  transition: cx 0s ease-out, cy 0s ease-out;
}
</style>
