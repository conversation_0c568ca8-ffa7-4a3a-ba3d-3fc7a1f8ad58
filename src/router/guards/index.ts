/**
 * 路由守卫统一入口
 */
import type { Router } from 'vue-router'
import { setupBeforeEachGuard } from './beforeEach'
import { setupAfterEachGuard, setupRouterErrorHandler } from './afterEach'

/**
 * 设置所有路由守卫
 */
export function setupRouterGuards(router: Router): void {
  // 设置前置守卫
  setupBeforeEachGuard(router)

  // 设置后置守卫
  setupAfterEachGuard(router)

  // 设置错误处理
  setupRouterErrorHandler(router)
}

// 导出所有守卫相关的函数
export * from './beforeEach'
export * from './afterEach'
