import request from '@/utils/request'

// 查询OSS对象存储列表
export function listOss(data) {
  return request({
    url: '/system/oss/list',
    method: 'post',
    data
  })
}

export function pageList(data) {
  return request({
    url: '/system/oss/pageList',
    method: 'post',
    data
  })
}

// 查询OSS对象存储详细
export function getOss(ossId) {
  return request({
    url: '/system/oss/' + ossId,
    method: 'get'
  })
}

// 新增OSS对象存储
export function addOss(data) {
  return request({
    url: '/system/oss',
    method: 'post',
    data: data
  })
}

// 修改OSS对象存储
export function updateOss(data) {
  return request({
    url: '/system/oss',
    method: 'put',
    data: data
  })
}

export function moveTag(data) {
  return request({
    url: '/system/oss/moveTag',
    method: 'put',
    data: data
  })
}

// 删除OSS对象存储
export function delOss(ossId) {
  return request({
    url: '/system/oss/' + ossId,
    method: 'delete'
  })
}

export function uploadImageFromUrl(params) {
  return request({
    url: '/system/oss/uploadImageFromUrl',
    method: 'get',
    params
  })
}
