import request from '@/utils/request'

// 查询国际化列表
export function listInternational(data) {
  return request({
    url: '/system/international/list',
    method: 'post',
    data
  })
}

// 查询国际化详细
export function getInternational(id) {
  return request({
    url: '/system/international/' + id,
    method: 'get'
  })
}

// 根据字典翻译
export function translateDict(id) {
  return request({
    url: '/system/international/translateDict',
    method: 'get'
  })
}

// 新增国际化
export function addInternational(data) {
  return request({
    url: '/system/international',
    method: 'post',
    data: data
  })
}

// 修改国际化
export function updateInternational(data) {
  return request({
    url: '/system/international',
    method: 'put',
    data: data
  })
}

// 删除国际化
export function delInternational(id) {
  return request({
    url: '/system/international/' + id,
    method: 'delete'
  })
}

// 获取所有国际化
export function getAllI18n() {
  return request({
    url: '/system/international/getAllI18n',
    method: 'get'
  })
}
