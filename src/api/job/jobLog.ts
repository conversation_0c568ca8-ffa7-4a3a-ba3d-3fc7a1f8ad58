import request from '@/utils/request'

// 查询调度任务日志
export function list(data) {
  return request({
    url: '/xxl/joblog/list',
    method: 'get',
    params: data
  })
}

/**
 * 获取分组下的所有job
 * @param data
 * @returns {*}
 */
export function getJobsByGroup(data) {
  return request({
    url: '/xxl/joblog/getJobsByGroup',
    method: 'get',
    params: data
  })
}

/**
 * 查看执行日志
 * @param data
 * @returns {*}
 */
export function getById(data) {
  return request({
    url: '/xxl/joblog/getById',
    method: 'get',
    params: data
  })
}

/**
 * 获取日志内容
 * @param data
 * @returns {*}
 */
export function getLogContent(data) {
  return request({
    url: '/xxl/joblog/logDetailCat',
    method: 'get',
    params: data
  })
}

/**
 * 清除日志
 * @param data
 * @returns {*}
 */
export function clearLog(data) {
  return request({
    url: '/xxl/joblog/clearLog',
    method: 'get',
    params: data
  })
}

/**
 * 终止日志
 * @param data
 * @returns {*}
 */
export function stopLog(data) {
  return request({
    url: '/xxl/joblog/logKill',
    method: 'get',
    params: data
  })
}
