import request from '@/utils/request'

// 查询执行器列表
export function list(data) {
  return request({
    url: '/xxl/jobgroup/list',
    method: 'get',
    params: data
  })
}

/**
 * 查询所有执行器列表
 * @param data
 * @returns {*}
 */
export function listAll(data) {
  return request({
    url: '/xxl/jobgroup/listAll',
    method: 'get'
  })
}

/**
 * 删除执行器
 * @param data
 * @returns {*}
 */
export function remove(data) {
  return request({
    url: '/xxl/jobgroup/remove',
    method: 'get',
    params: data
  })
}

/**
 * 新增执行器
 * @param data
 * @returns {*}
 */
export function save(data) {
  return request({
    url: '/xxl/jobgroup/save',
    method: 'get',
    params: data
  })
}

/**
 * 获取执行器详情
 * @param data
 * @returns {*}
 */
export function getById(data) {
  return request({
    url: '/xxl/jobgroup/getById',
    method: 'get',
    params: data
  })
}
