import request from '@/utils/request'

// 查询
export function list(data) {
  return request({
    url: '/xxl/user/list',
    method: 'get',
    params: data
  })
}

/**
 * 删除
 * @param data
 * @returns {*}
 */
export function remove(data) {
  return request({
    url: '/xxl/user/deleteById',
    method: 'get',
    params: data
  })
}

/**
 * 新增
 * @param data
 * @returns {*}
 */
export function save(data) {
  return request({
    url: '/xxl/user/add',
    method: 'get',
    params: data
  })
}

/**
 * 获取详情
 * @param data
 * @returns {*}
 */
export function getById(data) {
  return request({
    url: '/xxl/user/getById',
    method: 'get',
    params: data
  })
}

/**
 * 修改用户信息
 * @param data
 * @returns {*}
 */
export function update(data) {
  return request({
    url: '/xxl/user/updateById',
    method: 'get',
    params: data
  })
}
