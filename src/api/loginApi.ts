import request from '@/utils/request'
import { BaseResult } from '@/types/axios'

// 登录方法
export function loginUser(data) {
  return request<BaseResult>({
    url: '/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data
  })
}

//
// /**login
//  * 判断是否开启TOTP
//  * @param username
//  * @param password
//  * @param code
//  * @param uuid
//  * @returns {*}
//  */
export function isEnableTotp(username, password) {
  const data = {
    username,
    password
  }
  return request({
    url: '/isEnableTotp',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data
  })
}

// // 注册方法
// export function register(data) {
//   return request({
//     url: '/register',
//     headers: {
//       isToken: false
//     },
//     method: 'post',
//     data: data
//   })
// }
//
// // 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get'
  })
}
