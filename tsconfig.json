{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "noImplicitAny": false,

    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "types": ["vite/client", "node", "element-plus/global","vite-plugin-svg-icons/client"],
    "skipLibCheck": true, // 针对element-plus的打包校验
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@views/*": ["src/views/*"],
      "@imgs/*": ["src/assets/img/*"],
      "@icons/*": ["src/assets/icons/*"],
      "@utils/*": ["src/utils/*"],
      "@stores/*": ["src/store/*"],
      "@plugins/*": ["src/plugins/*"],
      "@styles/*": ["src/assets/styles/*"]
    }
  },
  "include": ["src/**/*", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
